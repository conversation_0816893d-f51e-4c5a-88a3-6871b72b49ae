package org.thingsboard.server.controller.base;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.dao.base.IBaseDrawingBoardManagementService;
import org.thingsboard.server.dao.model.sql.base.BaseDrawingBoardManagement;
import org.thingsboard.server.dao.util.imodel.response.ExcelFileInfo;
import org.thingsboard.server.dao.util.imodel.response.base.BaseDrawingBoardManagementExportVO;
import org.thingsboard.server.utils.imodel.annotations.IStarController2;

import java.util.ArrayList;
import java.util.List;

/**
 * 平台管理-画板管理Controller
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@Api(tags = "平台管理-画板管理")
@RestController
@RequestMapping("api/base/drawing/management/ex")
@IStarController2
public class BaseDrawingBoardManagementExController extends BaseController {

    @Autowired
    private IBaseDrawingBoardManagementService baseDrawingBoardManagementService;

    @ApiOperation(value = "导出画板")
    @GetMapping("/export")
    public ExcelFileInfo export(BaseDrawingBoardManagement request) throws ThingsboardException {
        List<BaseDrawingBoardManagement> list = baseDrawingBoardManagementService.exportList(request);

        // 使用ExcelFileInfo导出
        return ExcelFileInfo.of("画板管理信息", convertToExportVO(list))
                .nextTitle("name", "工艺图名称")
                .nextTitle("description", "描述")
                .nextTitle("status", "状态")
                .nextTitle("version", "版本号")
                .nextTitle("url", "工艺图文件路径");
    }

    private List<BaseDrawingBoardManagementExportVO> convertToExportVO(List<BaseDrawingBoardManagement> list) {
        // 转换为导出VO
        List<BaseDrawingBoardManagementExportVO> result = new ArrayList<>();
        if (list != null && !list.isEmpty()) {
            for (BaseDrawingBoardManagement entity : list) {
                BaseDrawingBoardManagementExportVO vo = new BaseDrawingBoardManagementExportVO();
                BeanUtils.copyProperties(entity, vo);
                result.add(vo);
            }
        }
        return result;
    }
}
