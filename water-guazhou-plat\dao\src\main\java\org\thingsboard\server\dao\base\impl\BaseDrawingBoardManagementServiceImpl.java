package org.thingsboard.server.dao.base.impl;

import java.util.Collections;
import java.util.List;
import java.util.UUID;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.base.IBaseDrawingBoardManagementService;
import org.thingsboard.server.dao.model.sql.base.BaseDrawingBoardManagement;
import org.thingsboard.server.dao.model.sql.base.BaseTemplateFile;
import org.thingsboard.server.dao.sql.base.BaseDrawingBoardManagementMapper;
import org.thingsboard.server.dao.util.imodel.query.base.BaseDrawingBoardManagementPageRequest;

/**
 * 平台管理-画板管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@Service
public class BaseDrawingBoardManagementServiceImpl implements IBaseDrawingBoardManagementService {

    @Autowired
    private BaseDrawingBoardManagementMapper baseDrawingBoardManagementMapper;

    /**
     * 查询平台管理-画板管理
     *
     * @param id 平台管理-画板管理主键
     * @return 平台管理-画板管理
     */
    @Override
    public BaseDrawingBoardManagement selectBaseDrawingBoardManagementById(String id) {
        return baseDrawingBoardManagementMapper.selectBaseDrawingBoardManagementById(id);
    }

    /**
     * 查询平台管理-画板管理列表
     *
     * @param baseDrawingBoardManagement 平台管理-画板管理
     * @return 平台管理-画板管理
     */
    @Override
    public IPage<BaseDrawingBoardManagement> selectBaseDrawingBoardManagementList(BaseDrawingBoardManagementPageRequest baseDrawingBoardManagement) {
        return baseDrawingBoardManagementMapper.selectBaseDrawingBoardManagementList(baseDrawingBoardManagement);
    }

    /**
     * 新增平台管理-画板管理
     *
     * @param baseDrawingBoardManagement 平台管理-画板管理
     * @return 结果
     */
    @Override
    public int insertBaseDrawingBoardManagement(BaseDrawingBoardManagement baseDrawingBoardManagement) {
        baseDrawingBoardManagement.setId(UUID.randomUUID().toString().replace("-", ""));
        return baseDrawingBoardManagementMapper.insertBaseDrawingBoardManagement(baseDrawingBoardManagement);
    }

    /**
     * 修改平台管理-画板管理
     *
     * @param baseDrawingBoardManagement 平台管理-画板管理
     * @return 结果
     */
    @Override
    public int updateBaseDrawingBoardManagement(BaseDrawingBoardManagement baseDrawingBoardManagement) {
        return baseDrawingBoardManagementMapper.updateBaseDrawingBoardManagement(baseDrawingBoardManagement);
    }

    /**
     * 批量删除平台管理-画板管理
     *
     * @param ids 需要删除的平台管理-画板管理主键
     * @return 结果
     */
    @Override
    public int deleteBaseDrawingBoardManagementByIds(List<String> ids) {
        return baseDrawingBoardManagementMapper.deleteBaseDrawingBoardManagementByIds(ids);
    }

    /**
     * 删除平台管理-画板管理信息
     *
     * @param id 平台管理-画板管理主键
     * @return 结果
     */
    @Override
    public int deleteBaseDrawingBoardManagementById(String id) {
        return baseDrawingBoardManagementMapper.deleteBaseDrawingBoardManagementById(id);
    }

    @Override
    public void batchImport(List<BaseDrawingBoardManagement> list) {
        if (list == null || list.isEmpty()) {
            return;
        }
        for (BaseDrawingBoardManagement baseDrawingBoardManagement : list) {
            baseDrawingBoardManagement.setId(UUID.randomUUID().toString().replace("-", ""));
        }
        baseDrawingBoardManagementMapper.batchInsert(list);
    }

    @Override
    public List<BaseDrawingBoardManagement> exportList(BaseDrawingBoardManagement request) {
        return baseDrawingBoardManagementMapper.exportList(request);
    }
}
