package org.thingsboard.server.dao.remoteControl;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.remoteControl.RemoteControlDeviceSetting;

import java.util.List;

/**
 * 远程控制设备Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@Mapper
public interface RemoteControlDeviceMapper extends BaseMapper<RemoteControlDeviceSetting> {

    /**
     * 分页查询远程控制设备
     *
     * @param page 分页参数
     * @param waterPlantId 水厂ID
     * @param deviceType 设备类型
     * @param location 设备位置
     * @param tenantId 租户ID
     * @return 分页结果
     */
    IPage<RemoteControlDeviceSetting> selectPageList(
            Page<RemoteControlDeviceSetting> page,
            @Param("waterPlantId") String waterPlantId,
            @Param("deviceType") String deviceType,
            @Param("location") String location,
            @Param("tenantId") String tenantId
    );

    /**
     * 根据水厂ID查询设备列表
     *
     * @param waterPlantId 水厂ID
     * @param tenantId 租户ID
     * @return 设备列表
     */
    List<RemoteControlDeviceSetting> selectByWaterPlantId(
            @Param("waterPlantId") String waterPlantId,
            @Param("tenantId") String tenantId
    );

    /**
     * 根据设备类型查询设备列表
     *
     * @param deviceType 设备类型
     * @param tenantId 租户ID
     * @return 设备列表
     */
    List<RemoteControlDeviceSetting> selectByDeviceType(
            @Param("deviceType") String deviceType,
            @Param("tenantId") String tenantId
    );

    /**
     * 统计设备类型数量
     *
     * @param tenantId 租户ID
     * @return 设备类型统计
     */
    List<java.util.Map<String, Object>> countByDeviceType(@Param("tenantId") String tenantId);

    /**
     * 根据设备ID查询扩展信息
     *
     * @param deviceId 设备ID
     * @param tenantId 租户ID
     * @return 设备扩展信息
     */
    RemoteControlDeviceSetting selectByDeviceId(
            @Param("deviceId") String deviceId,
            @Param("tenantId") String tenantId
    );
}
