package org.thingsboard.server.controller.smartPipe;

import java.util.List;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.smartPipe.PipelineNetworkEngineeringData;
import org.thingsboard.server.dao.smartPipe.IPipelineNetworkEngineeringDataService;
import org.thingsboard.server.dao.util.imodel.query.smartPipe.PipelineNetworkEngineeringDataPageRequest;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.service.aspect.annotation.MonitorPerformance;

/**
 * 管网采集-工程数据Controller
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Api(tags = "管网采集-工程数据")
@RestController
@RequestMapping("api/pipeline/engineering/data")
public class PipelineNetworkEngineeringDataController extends BaseController {

    @Autowired
    private IPipelineNetworkEngineeringDataService pipelineNetworkEngineeringDataService;

    /**
     * 查询管网采集-工程数据列表
     */
    @MonitorPerformance(description = "管网采集-查询工程数据列表")
    @ApiOperation(value = "查询工程数据列表")
    @GetMapping("/list")
    public IstarResponse list(PipelineNetworkEngineeringDataPageRequest pipelineNetworkEngineeringData) {
        return IstarResponse.ok(pipelineNetworkEngineeringDataService.selectPipelineNetworkEngineeringDataList(pipelineNetworkEngineeringData));
    }

    /**
     * 获取管网采集-工程数据详细信息
     */
    @MonitorPerformance(description = "管网采集-查询工程数据详情")
    @ApiOperation(value = "查询工程数据详情")
    @GetMapping(value = "/getDetail")
    public IstarResponse getInfo(String id) {
        return IstarResponse.ok(pipelineNetworkEngineeringDataService.selectPipelineNetworkEngineeringDataById(id));
    }

    /**
     * 新增管网采集-工程数据
     */
    @MonitorPerformance(description = "管网采集-新增工程数据")
    @ApiOperation(value = "新增工程数据")
    @PostMapping("/add")
    public IstarResponse add(@RequestBody PipelineNetworkEngineeringData pipelineNetworkEngineeringData) throws ThingsboardException {
        pipelineNetworkEngineeringData.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
        return IstarResponse.ok(pipelineNetworkEngineeringDataService.insertPipelineNetworkEngineeringData(pipelineNetworkEngineeringData));
    }

    /**
     * 修改管网采集-工程数据
     */
    @MonitorPerformance(description = "管网采集-修改工程数据")
    @ApiOperation(value = "修改工程数据")
    @PostMapping("/edit")
    public IstarResponse edit(@RequestBody PipelineNetworkEngineeringData pipelineNetworkEngineeringData) {
        return IstarResponse.ok(pipelineNetworkEngineeringDataService.updatePipelineNetworkEngineeringData(pipelineNetworkEngineeringData));
    }

    /**
     * 删除管网采集-工程数据
     */
    @MonitorPerformance(description = "管网采集-删除工程数据")
    @ApiOperation(value = "删除工程数据")
    @DeleteMapping("/deleteIds")
    public IstarResponse remove(@RequestBody List<String> ids) {
        return IstarResponse.ok(pipelineNetworkEngineeringDataService.deletePipelineNetworkEngineeringDataByIds(ids));
    }
}
