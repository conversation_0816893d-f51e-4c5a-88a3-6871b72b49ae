package org.thingsboard.server.dao.assessmenttable;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 考核表明细项DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class AssessmentTableItemDto {

    /**
     * id
     */
    @JsonProperty("id")
    private String id;

    /**
     * 考核表ID
     */
    @JsonProperty("assessmentTableId")
    private String assessmentTableId;

    /**
     * 指标名称
     */
    @JsonProperty("indicatorName")
    private String indicatorName;

    /**
     * 指标描述
     */
    @JsonProperty("indicatorDesc")
    private String indicatorDesc;

    /**
     * 指标类型
     * 1: 定量指标
     * 2: 定性指标
     */
    @JsonProperty("indicatorType")
    private String indicatorType;

    /**
     * 指标单位
     */
    @JsonProperty("indicatorUnit")
    private String indicatorUnit;

    /**
     * 指标权重
     */
    @JsonProperty("weight")
    private Double weight;

    /**
     * 指标目标值
     */
    @JsonProperty("targetValue")
    private Double targetValue;

    /**
     * 指标实际值
     */
    @JsonProperty("actualValue")
    private Double actualValue;

    /**
     * 得分
     */
    @JsonProperty("score")
    private Double score;

    /**
     * 评分人
     */
    @JsonProperty("scorer")
    private String scorer;

    /**
     * 评分时间
     */
    @JsonProperty("scoreTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date scoreTime;

    /**
     * 备注
     */
    @JsonProperty("remark")
    private String remark;

    /**
     * 创建时间
     */
    @JsonProperty("createTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 排序号
     */
    @JsonProperty("sortNum")
    private Integer sortNum;
} 