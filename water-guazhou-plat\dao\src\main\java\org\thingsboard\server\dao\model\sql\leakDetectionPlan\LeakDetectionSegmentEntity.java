package org.thingsboard.server.dao.model.sql.leakDetectionPlan;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 探漏分段实体类
 */
@Data
@TableName("leak_detection_segment")
@NoArgsConstructor
@AllArgsConstructor
@Slf4j
public class LeakDetectionSegmentEntity {

    /**
     * 主键ID
     */
    @TableId(type = IdType.INPUT)
    private String id;

    /**
     * 所属方案ID
     */
    @TableField("plan_id")
    private String planId;

    /**
     * 分段名称
     */
    @TableField("name")
    private String name;

    /**
     * 分段编码
     */
    @TableField("segment_code")
    private String segmentCode;

    /**
     * 管段长度(米)
     */
    @TableField("length")
    private BigDecimal length;

    /**
     * 管道材质
     */
    @TableField("pipe_material")
    private String pipeMaterial;

    /**
     * 管径(mm)
     */
    @TableField("pipe_diameter")
    private Integer pipeDiameter;

    /**
     * 起始节点
     */
    @TableField("start_node")
    private String startNode;

    /**
     * 终止节点
     */
    @TableField("end_node")
    private String endNode;

    /**
     * 优先级：1-高，2-中，3-低
     */
    @TableField("priority")
    private Integer priority;

    /**
     * 状态：PLANNED-计划中, EXECUTING-执行中, COMPLETED-已完成, CANCELED-已取消
     */
    @TableField("status")
    private String status;

    /**
     * 计划探测时间
     */
    @TableField("schedule_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date scheduleTime;

    /**
     * 实际探测时间
     */
    @TableField("actual_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date actualTime;

    /**
     * 漏点数
     */
    @TableField("leak_point_count")
    private Integer leakPointCount;

    /**
     * 漏损率(%)
     */
    @TableField("leak_level")
    private BigDecimal leakLevel;

    /**
     * 执行人
     */
    @TableField("executor")
    private String executor;

    /**
     * 探测方法
     */
    @TableField("detection_method")
    private String detectionMethod;

    /**
     * 探测结果
     */
    @TableField("result")
    private String result;

    /**
     * 分段描述
     */
    @TableField("description")
    private String description;

    /**
     * 绑定的设备ID列表
     */
    @TableField("device_ids")
    private String deviceIds;


    /**
     * 创建人
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField("created_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdTime;

    /**
     * 更新人
     */
    @TableField("updated_by")
    private String updatedBy;

    /**
     * 更新时间
     */
    @TableField("updated_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedTime;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    @TableField("deleted")
    @TableLogic
    private Integer deleted;

    /**
     * 方案名称（非数据库字段）
     */
    @TableField(exist = false)
    private String planName;

    /**
     * 分区ID（非数据库字段）
     */
    @TableField(exist = false)
    private String partitionId;

    /**
     * 方案负责人（非数据库字段）
     */
    @TableField(exist = false)
    private String planResponsiblePerson;
} 