package org.thingsboard.server.dao.sql.leakDetectionPlan;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import org.thingsboard.server.dao.model.sql.leakDetectionPlan.LeakDetectionPlanEntity;

import java.util.List;

/**
 * 探漏方案DAO接口
 */
@Mapper
@Repository
public interface LeakDetectionPlanDao extends BaseMapper<LeakDetectionPlanEntity> {

    /**
     * 分页查询探漏方案
     *
     * @param page      分页参数
     * @param name      方案名称
     * @param areaCode  区域编码
     * @param status    状态
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 分页结果
     */
    Page<LeakDetectionPlanEntity> queryPlansByPage(Page<LeakDetectionPlanEntity> page,
                                                  @Param("name") String name,
                                                  @Param("areaCode") String areaCode,
                                                  @Param("status") String status,
                                                  @Param("startTime") String startTime,
                                                  @Param("endTime") String endTime);

    /**
     * 根据区域编码查询探漏方案
     *
     * @param areaCode 区域编码
     * @return 探漏方案列表
     */
    List<LeakDetectionPlanEntity> findByAreaCode(@Param("areaCode") String areaCode);

    /**
     * 根据状态查询探漏方案
     *
     * @param status 状态
     * @return 探漏方案列表
     */
    List<LeakDetectionPlanEntity> findByStatus(@Param("status") String status);
    
    /**
     * 逻辑删除探漏方案
     *
     * @param id 方案ID
     * @return 影响行数
     */
    int deleteLeakDetectionPlan(@Param("id") String id);
} 