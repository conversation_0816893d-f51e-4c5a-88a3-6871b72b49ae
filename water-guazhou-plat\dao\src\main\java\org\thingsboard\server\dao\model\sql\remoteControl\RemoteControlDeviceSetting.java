package org.thingsboard.server.dao.model.sql.remoteControl;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 远程控制设备扩展信息实体类
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@Data
@TableName("remote_control_device_setting")
public class RemoteControlDeviceSetting {

    /**
     * 主键ID
     */
    @TableId
    private String id;

    /**
     * 设备ID
     */
    private String deviceId;

    /**
     * 水厂ID
     */
    private String waterPlantId;

    /**
     * 水厂名称
     */
    private String waterPlantName;

    /**
     * 设备类型(pump/valve/monitor/controller)
     */
    private String deviceType;

    /**
     * 设备位置
     */
    private String location;

    /**
     * 安装日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date installDate;

    /**
     * 制造商
     */
    private String manufacturer;

    /**
     * 型号
     */
    private String model;

    /**
     * 设备描述
     */
    private String description;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}
