package org.thingsboard.server.dao.assessmenttable;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelIgnore;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.thingsboard.server.common.data.id.TenantId;

import java.util.Date;
import java.util.List;

/**
 * 考核表DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class AssessmentTableDto {

    /**
     * id
     */
    @ExcelIgnore
    @JsonProperty("id")
    private String id;

    /**
     * 租户ID
     */
    @ExcelIgnore
    @JsonProperty("tenantId")
    private TenantId tenantId;

    /**
     * 考核表名称
     */
    @Excel(name = "考核表名称", width = 20, orderNum = "1")
    @JsonProperty("name")
    private String name;

    /**
     * 区域
     */
    @Excel(name = "区域", width = 15, orderNum = "2")
    @JsonProperty("region")
    private String region;

    /**
     * 所在分区
     */
    @Excel(name = "所在分区", width = 15, orderNum = "3")
    @JsonProperty("partition")
    private String partition;

    /**
     * 考核周期（年月，格式：YYYY-MM）
     */
    @Excel(name = "考核周期", width = 15, orderNum = "4")
    @JsonProperty("period")
    private String period;
    
    /**
     * 考核类型
     * 1: 水质考核
     * 2: 供水安全考核
     * 3: 管网漏损考核
     * 4: 能耗考核
     * 5: 设备运行考核
     * 6: 综合评估
     */
    @Excel(name = "考核类型", width = 15, orderNum = "5", replace = {"水质考核_1", "供水安全考核_2", "管网漏损考核_3", "能耗考核_4", "设备运行考核_5", "综合评估_6"})
    @JsonProperty("assessmentType")
    private String assessmentType;
    
    /**
     * 考核等级
     * A: 优秀
     * B: 良好
     * C: 合格
     * D: 不合格
     */
    @Excel(name = "考核等级", width = 10, orderNum = "6", replace = {"优秀_A", "良好_B", "合格_C", "不合格_D"})
    @JsonProperty("assessmentLevel")
    private String assessmentLevel;
    
    /**
     * 考核状态
     * 0: 草稿
     * 1: 已提交
     * 2: 审核中
     * 3: 已通过
     * 4: 已驳回
     */
    @Excel(name = "状态", width = 10, orderNum = "7", replace = {"草稿_0", "已提交_1", "审核中_2", "已通过_3", "已驳回_4"})
    @JsonProperty("status")
    private String status;
    
    /**
     * 总分
     */
    @Excel(name = "总分", width = 10, orderNum = "8")
    @JsonProperty("totalScore")
    private Double totalScore;
    
    /**
     * 审核人
     */
//    @Excel(name = "审核人", width = 15, orderNum = "11")
    @JsonProperty("reviewer")
    private String reviewer;
    
    /**
     * 审核时间
     */
//    @Excel(name = "审核时间", width = 20, orderNum = "12", format = "yyyy-MM-dd HH:mm:ss")
    @JsonProperty("reviewTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date reviewTime;

    /**
     * 备注
     */
    @Excel(name = "备注", width = 25, orderNum = "13")
    @JsonProperty("remark")
    private String remark;

    /**
     * 创建人
     */
//    @Excel(name = "创建人", width = 15, orderNum = "9")
    @JsonProperty("creator")
    private String creator;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间", width = 20, orderNum = "10", format = "yyyy-MM-dd HH:mm:ss")
    @JsonProperty("createTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    
    /**
     * 考核表明细项列表
     */
    @ExcelIgnore
    @JsonProperty("items")
    private List<AssessmentTableItemDto> items;
} 