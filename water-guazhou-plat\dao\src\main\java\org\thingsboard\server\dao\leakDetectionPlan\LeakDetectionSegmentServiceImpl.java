package org.thingsboard.server.dao.leakDetectionPlan;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.thingsboard.server.dao.model.sql.leakDetectionPlan.LeakDetectionSegmentEntity;
import org.thingsboard.server.dao.sql.leakDetectionPlan.LeakDetectionSegmentDao;
import org.thingsboard.server.dao.model.DTO.DeviceDTO;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 探漏分段服务实现类
 */
@Service
@Slf4j
public class LeakDetectionSegmentServiceImpl implements LeakDetectionSegmentService {

    @Autowired
    private LeakDetectionSegmentDao leakDetectionSegmentDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public LeakDetectionSegmentEntity saveOrUpdateSegment(LeakDetectionSegmentEntity segment) {
        log.info("Save or update leak detection segment: {}", segment);
        
        if (StringUtils.isEmpty(segment.getPlanId())) {
            throw new IllegalArgumentException("方案ID不能为空");
        }
        
        // 注意：不需要手动处理deviceIds和deviceIdsJson的转换
        // 实体类的setDeviceIds方法会自动处理这个转换
        
        if (StringUtils.isEmpty(segment.getId())) {
            // 新增
            segment.setId(UUID.randomUUID().toString());
            segment.setCreatedTime(new Date());
            segment.setDeleted(0);
            
            leakDetectionSegmentDao.insert(segment);
        } else {
            // 更新
            LeakDetectionSegmentEntity oldSegment = leakDetectionSegmentDao.selectById(segment.getId());
            if (oldSegment == null) {
                throw new IllegalArgumentException("探漏分段不存在");
            }
            
            segment.setUpdatedTime(new Date());
            segment.setDeleted(oldSegment.getDeleted());
            segment.setCreatedTime(oldSegment.getCreatedTime());
            segment.setCreatedBy(oldSegment.getCreatedBy());
            
            leakDetectionSegmentDao.updateById(segment);
        }
        
        return segment;
    }

    @Override
    public LeakDetectionSegmentEntity getSegmentById(String id) {
        log.info("Get leak detection segment by id: {}", id);
        LeakDetectionSegmentEntity entity = leakDetectionSegmentDao.selectById(id);
        // 注意：不需要手动处理deviceIdsJson到deviceIds的转换
        // 实体类的getDeviceIds方法会自动处理这个转换
        return entity;
    }

    @Override
    public List<LeakDetectionSegmentEntity> findByPlanId(String planId) {
        log.info("Find leak detection segments by plan id: {}", planId);
        // 注意：不需要手动处理deviceIdsJson到deviceIds的转换
        // 实体类的getDeviceIds方法会自动处理这个转换
        return leakDetectionSegmentDao.findByPlanId(planId);
    }

    @Override
    public Page<LeakDetectionSegmentEntity> querySegmentsByPage(Page<LeakDetectionSegmentEntity> page,
                                                             String planId,
                                                             String name,
                                                             String segmentCode,
                                                             String status,
                                                             Integer priority) {
        log.info("Query leak detection segments by page, planId: {}, name: {}, segmentCode: {}, status: {}, priority: {}",
                planId, name, segmentCode, status, priority);
        // 注意：不需要手动处理deviceIdsJson到deviceIds的转换
        // 实体类的getDeviceIds方法会自动处理这个转换
        return leakDetectionSegmentDao.querySegmentsByPage(page, planId, name, segmentCode, status, priority);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteSegment(String id) {
        log.info("Delete leak detection segment by id: {}", id);
        
        // 直接使用新增的删除方法
        return leakDetectionSegmentDao.deleteLeakDetectionSegment(id) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeleteSegments(List<String> ids) {
        log.info("Batch delete leak detection segments: {}", ids);
        
        if (ids == null || ids.isEmpty()) {
            return false;
        }
        
        for (String id : ids) {
            deleteSegment(id);
        }
        
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByPlanId(String planId) {
        log.info("Delete leak detection segments by plan id: {}", planId);
        return leakDetectionSegmentDao.deleteByPlanId(planId) > 0;
    }

    @Override
    public boolean updateSegmentStatus(String id, String status) {
        log.info("Update leak detection segment status, id: {}, status: {}", id, status);
        
        if (StringUtils.isEmpty(id) || StringUtils.isEmpty(status)) {
            return false;
        }
        
        LambdaUpdateWrapper<LeakDetectionSegmentEntity> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(LeakDetectionSegmentEntity::getId, id)
                    .set(LeakDetectionSegmentEntity::getStatus, status)
                    .set(LeakDetectionSegmentEntity::getUpdatedTime, new Date());
        
        return leakDetectionSegmentDao.update(null, updateWrapper) > 0;
    }

    @Override
    public boolean update(LambdaUpdateWrapper<LeakDetectionSegmentEntity> updateWrapper) {
        log.info("Update segment with wrapper");
        return leakDetectionSegmentDao.update(null, updateWrapper) > 0;
    }

    @Override
    public List<Map<String, Object>> countSegmentStatusByPlanId(String planId) {
        log.info("Count leak detection segment status by plan id: {}", planId);
        
        List<Object> result = leakDetectionSegmentDao.countSegmentStatusByPlanId(planId);
        
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> statusCounts = result.stream()
                .map(item -> (Map<String, Object>) item)
                .collect(Collectors.toList());
        
        return statusCounts;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSegmentDevices(String id, List<String> deviceIds) {
        log.info("Update segment devices, id: {}, deviceIds: {}", id, deviceIds);
        
        if (StringUtils.isEmpty(id)) {
            return false;
        }
        
        LeakDetectionSegmentEntity segment = leakDetectionSegmentDao.selectById(id);
        if (segment == null) {
            log.error("Segment not found: {}", id);
            return false;
        }
        
        // 将设备ID列表转换为逗号分隔的字符串
        String deviceIdsStr = deviceIds != null ? String.join(",", deviceIds) : "";
        
        // 更新设备ID字符串
        segment.setDeviceIds(deviceIdsStr);
        
        return leakDetectionSegmentDao.updateById(segment) > 0;
    }
    
    @Override
    public Page<DeviceDTO> getAvailableDevices(String partitionId, List<String> boundDeviceIds, 
                                             Integer page, Integer size, String name, String type) {
        log.info("Get available devices, partitionId: {}, boundDeviceIds: {}, name: {}, type: {}", 
                partitionId, boundDeviceIds, name, type);
        
        try {
            // 创建分页对象
            Page<DeviceDTO> pageParam = new Page<>(page, size);
            
            // 如果boundDeviceIds为null，初始化为空列表
            if (boundDeviceIds == null) {
                boundDeviceIds = new ArrayList<>();
            }
            
            // 调用DAO层方法查询可用设备
            return leakDetectionSegmentDao.queryAvailableDevices(pageParam, partitionId, boundDeviceIds, name, type);
        } catch (Exception e) {
            log.error("Failed to get available devices", e);
            // 返回空结果
            return new Page<>(page, size);
        }
    }
    
    @Override
    public List<DeviceDTO> getDeviceDetailsByIds(List<String> deviceIds, String partitionId) {
        log.info("Get device details by ids: {}, partitionId: {}", deviceIds, partitionId);
        
        if (deviceIds == null || deviceIds.isEmpty()) {
            return new ArrayList<>();
        }
        
        try {
            // 调用DAO层方法查询设备详情
            return leakDetectionSegmentDao.queryDeviceDetailsByIds(deviceIds, partitionId);
        } catch (Exception e) {
            log.error("Failed to get device details", e);
            
            // 发生异常时，构造基本设备信息
            List<DeviceDTO> fallbackList = new ArrayList<>();
            for (String deviceId : deviceIds) {
                DeviceDTO deviceInfo = DeviceDTO.builder()
                    .deviceId(deviceId)
                    .name("设备" + deviceId.substring(0, Math.min(8, deviceId.length()))) // 使用ID前8位作为名称
                    .type("1") // 默认类型
                    .partitionId(partitionId) // 设置分区ID
                    .build();
                fallbackList.add(deviceInfo);
            }
            
            return fallbackList;
        }
    }
} 