package org.thingsboard.server.dao.repair;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.User;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.device.DeviceService;
import org.thingsboard.server.dao.model.DTO.TriggerDTO;
import org.thingsboard.server.dao.model.sql.*;
import org.thingsboard.server.dao.model.sql.shuiwu.assets.AssetsAccountEntity;
import org.thingsboard.server.dao.orderWork.WorkOrderService;
import org.thingsboard.server.dao.project.ProjectService;
import org.thingsboard.server.dao.shuiwu.assets.AssetsAccountService;
import org.thingsboard.server.dao.sql.repair.MaintenanceJobCRepository;
import org.thingsboard.server.dao.sql.repair.MaintenanceJobRepository;
import org.thingsboard.server.dao.sql.repair.MaintenanceJobTriggerRepository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class MaintenanceJobServiceImpl implements MaintenanceJobService {

    @Autowired
    private MaintenanceJobCRepository maintenanceJobCRepository;
    @Autowired
    private MaintenanceJobRepository maintenanceJobRepository;
    @Autowired
    private DeviceService deviceService;
    @Autowired
    private MaintenanceStandardService maintenanceStandardService;
    @Autowired
    private ProjectService projectService;
    @Autowired
    private MaintenanceJobTriggerRepository maintenanceJobTriggerRepository;
    @Autowired
    private WorkOrderService workOrderService;
    @Autowired
    private AssetsAccountService assetsAccountService;

    @Override
    public MaintenanceJobEntity detail(String id, TenantId tenantId) {
        // 查询主表
        MaintenanceJobEntity maintenanceJob = maintenanceJobRepository.findOne(id);

        // 查询子表
        if (maintenanceJob != null) {
            // 查询项目列表
            List<ProjectEntity> projectList = projectService.findByTenantId(tenantId);
            Map<String, ProjectEntity> projectMap = new HashMap<>();
            if (projectList != null && projectList.size() > 0) {
                projectList.forEach(p -> projectMap.put(p.getId(), p));
            }

            // 查询设备列表
            List<AssetsAccountEntity> assetsAccountList = assetsAccountService.findByTenantId(UUIDConverter.fromTimeUUID(tenantId.getId()));
            Map<String, AssetsAccountEntity> assetsAccountMap = new HashMap<>();
            if (assetsAccountList != null && assetsAccountList.size() > 0) {
                assetsAccountList.forEach(d -> assetsAccountMap.put(d.getId(), d));
            }

            // 查询保养项目列表
            List<MaintenanceStandardEntity> maintenanceStandardList = maintenanceStandardService.findAll("", tenantId);
            Map<String, MaintenanceStandardEntity> maintenanceStandardMap = new HashMap<>();
            if (maintenanceStandardList != null && maintenanceStandardList.size() > 0) {
                maintenanceStandardList.forEach(m -> maintenanceStandardMap.put(m.getId(), m));
            }
            if ("2".equals(maintenanceJob.getType())) {// 触发性任务
                MaintenanceJobTriggerEntity trigger = maintenanceJobTriggerRepository.findByMainId(id);
                if (trigger != null) {
                    String detail = trigger.getDetail();
                    trigger.setTriggerList(JSON.parseArray(detail, TriggerDTO.class));

                    ProjectEntity project = projectMap.get(trigger.getProjectId());
                    if (project != null) {
                        trigger.setProjectName(project.getName());
                    }

                    AssetsAccountEntity assetsAccountEntity = assetsAccountMap.get(trigger.getDeviceId());
                    if (assetsAccountEntity != null) {
                        trigger.setDeviceName(assetsAccountEntity.getDeviceName());
                    }

                }

                maintenanceJob.setTrigger(trigger);
            } else {// 固定日期和预防性任务
                List<MaintenanceJobCEntity> jobList = maintenanceJobCRepository.findByMainId(id);
                if (jobList != null && jobList.size() > 0) {
                    for (MaintenanceJobCEntity child : jobList) {
                        ProjectEntity project = projectMap.get(child.getProjectId());
                        if (project != null) {
                            child.setProjectName(project.getName());
                        }

                        AssetsAccountEntity assetsAccountEntity = assetsAccountMap.get(child.getDeviceId());
                        if (assetsAccountEntity != null) {
                            child.setDeviceName(assetsAccountEntity.getDeviceName());
                        }

                        MaintenanceStandardEntity maintenanceStandard = maintenanceStandardMap.get(child.getStandardName());
                        if (maintenanceStandard != null) {
                            child.setStandardName(maintenanceStandard.getName());
                        }
                    }

                    maintenanceJob.setJobList(jobList);
                }
            }

        }

        return maintenanceJob;
    }

    @Override
    public PageData<MaintenanceJobEntity> findList(int page, int size, String name, String deviceId, User currentUser) {
        // 分页参数
        PageRequest pageable = new PageRequest(page - 1, size, Sort.Direction.DESC, "createTime");

        Page<MaintenanceJobEntity> pageResult = maintenanceJobRepository.findList(name, UUIDConverter.fromTimeUUID(currentUser.getTenantId().getId()), deviceId, pageable);
        List<MaintenanceJobEntity> content = pageResult.getContent();

        List<WorkOrderEntity> orderList = workOrderService.findByTenantIdAndType(UUIDConverter.fromTimeUUID(currentUser.getTenantId().getId()), "2");
        Map<String, WorkOrderEntity> orderMap = new HashMap<>();

        if (orderList != null) {
            orderList.forEach(order -> orderMap.put(order.getContentId(), order));
        }

        for (MaintenanceJobEntity maintenanceJobEntity : content) {
            WorkOrderEntity order = orderMap.get(maintenanceJobEntity.getId());
            if (order != null) {
                maintenanceJobEntity.setOrderId(order.getId());
                maintenanceJobEntity.setCode(order.getCode());
            }
        }

        return new PageData<>(pageResult.getTotalElements(), content);
    }

    @Override
    public void remove(List<String> ids) {
        for (String id : ids) {
            // 删除主表
            maintenanceJobRepository.delete(id);

            // 删除子表
            maintenanceJobCRepository.removeByMainId(id);
        }
    }

    @Override
    public MaintenanceJobEntity save(MaintenanceJobEntity job) {
        return maintenanceJobRepository.save(job);
    }

    @Override
    public void save(List<MaintenanceJobCEntity> childList) {
        maintenanceJobCRepository.save(childList);
    }

    @Override
    public List<MaintenanceJobEntity> findJobByType(String type) {
        return maintenanceJobRepository.findByType(type);
    }

    @Override
    public void save(MaintenanceJobTriggerEntity jobC) {
        maintenanceJobTriggerRepository.save(jobC);
    }

    @Override
    public MaintenanceJobEntity findById(String contentId) {
        return maintenanceJobRepository.findOne(contentId);
    }


}
