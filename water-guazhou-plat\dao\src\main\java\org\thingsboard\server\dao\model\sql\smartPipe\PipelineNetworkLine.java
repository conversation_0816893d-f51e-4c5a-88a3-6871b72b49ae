package org.thingsboard.server.dao.model.sql.smartPipe;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 管网采集-管线对象 pipeline_network_line
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@ApiModel(value = "管线对象", description = "管网采集-管线对象")
@Data
public class PipelineNetworkLine {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 测点编号
     */
    @ApiModelProperty(value = "测点编号")
    private String code;

    /**
     * 管径
     */
    @ApiModelProperty(value = "管径")
    private String diameter;

    /**
     * 管材
     */
    @ApiModelProperty(value = "管材")
    private String material;

    /**
     * 起点点号
     */
    @ApiModelProperty(value = "起点点号")
    private String startDot;

    /**
     * 终点点号
     */
    @ApiModelProperty(value = "终点点号")
    private String endDot;

    /**
     * 起点标高
     */
    @ApiModelProperty(value = "起点标高")
    private String startElevation;

    /**
     * 终点标高
     */
    @ApiModelProperty(value = "终点标高")
    private String endElevation;

    /**
     * 起点深埋
     */
    @ApiModelProperty(value = "起点深埋")
    private String startDeep;

    /**
     * 终点深埋
     */
    @ApiModelProperty(value = "终点深埋")
    private String endDeep;

    /**
     * 所属工程id
     */
    @ApiModelProperty(value = "所属工程id")
    private String engineeringDataId;

    /**
     * 租户id
     */
    @ApiModelProperty(value = "租户id")
    private String tenantId;

    /**
     * 开始日期
     */
    @ApiModelProperty(value = "开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 结束日期
     */
    @ApiModelProperty(value = "结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
