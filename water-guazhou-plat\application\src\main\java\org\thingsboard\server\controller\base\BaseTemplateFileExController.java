package org.thingsboard.server.controller.base;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.dao.base.IBaseTemplateFileService;
import org.thingsboard.server.dao.model.sql.base.BaseTemplateFile;
import org.thingsboard.server.dao.util.imodel.query.base.BaseTemplateFilePageRequest;
import org.thingsboard.server.dao.util.imodel.response.ExcelFileInfo;
import org.thingsboard.server.dao.util.imodel.response.base.BaseTemplateFileExportVO;
import org.thingsboard.server.utils.imodel.annotations.IStarController2;

import java.util.ArrayList;
import java.util.List;

/**
 * 平台管理-模型文件Controller
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@Api(tags = "平台管理-模型文件")
@RestController
@RequestMapping("api/base/template/file/ex")
@IStarController2
public class BaseTemplateFileExController extends BaseController {

    @Autowired
    private IBaseTemplateFileService baseTemplateFileService;

    @ApiOperation(value = "导出模型文件")
    @GetMapping("/export")
    public ExcelFileInfo export(BaseTemplateFilePageRequest request) throws ThingsboardException {
        List<BaseTemplateFile> list = baseTemplateFileService.exportList(request);

        // 使用ExcelFileInfo导出
        return ExcelFileInfo.of("模型文件信息", convertToExportVO(list))
                .nextTitle("name", "模板名称")
                .nextTitle("code", "模板编码")
                .nextTitle("description", "描述")
                .nextTitle("type", "分类")
                .nextTitle("fileType", "文件类型")
                .nextTitle("fileUrl", "文件路径");
    }

    private List<BaseTemplateFileExportVO> convertToExportVO(List<BaseTemplateFile> list) {
        // 转换为导出VO
        List<BaseTemplateFileExportVO> result = new ArrayList<>();
        if (list != null && !list.isEmpty()) {
            for (BaseTemplateFile entity : list) {
                BaseTemplateFileExportVO vo = new BaseTemplateFileExportVO();
                BeanUtils.copyProperties(entity, vo);
                result.add(vo);
            }
        }
        return result;
    }
}
