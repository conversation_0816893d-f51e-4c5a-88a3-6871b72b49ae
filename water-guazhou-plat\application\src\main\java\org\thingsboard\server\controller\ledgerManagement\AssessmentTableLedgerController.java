package org.thingsboard.server.controller.ledgerManagement;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.dao.assessmenttable.AssessmentTableDto;
import org.thingsboard.server.dao.assessmenttable.AssessmentTableItemDto;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.assessmenttable.AssessmentTableService;
import org.thingsboard.server.dao.smartPipe.PartitionService;
import org.thingsboard.server.dao.model.DTO.PartitionTreeDTO;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.*;

/**
 * 考核表管理Controller
 */
@Api(value = "考核表管理接口", tags = {"考核表管理"})
@RestController
@RequestMapping("/api/ledger/assessment-table")
@Slf4j
public class AssessmentTableLedgerController extends BaseController {

    @Autowired
    private AssessmentTableService assessmentTableService;

    @Autowired
    private PartitionService partitionService;

    @ApiOperation(value = "保存考核表", notes = "创建或更新考核表")
    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN')")
    @PostMapping
    public ResponseEntity<AssessmentTableDto> saveAssessmentTable(
            @ApiParam(value = "考核表数据", required = true)
            @RequestBody AssessmentTableDto assessmentTableDto) throws ThingsboardException {
        try {
            TenantId tenantId = getCurrentUser().getTenantId();
            assessmentTableDto.setTenantId(tenantId);
            
            // 保存考核表
            AssessmentTableDto savedDto = checkNotNull(assessmentTableService.saveAssessmentTable(assessmentTableDto));
            return new ResponseEntity<>(savedDto, HttpStatus.OK);
        } catch (Exception e) {
            log.error("保存考核表失败", e);
            throw handleException(e);
        }
    }

    @ApiOperation(value = "根据ID获取考核表", notes = "根据ID获取考核表详情")
    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN')")
    @GetMapping("/{id}")
    public ResponseEntity<AssessmentTableDto> getAssessmentTableById(
            @ApiParam(value = "考核表ID", required = true)
            @PathVariable("id") String strId) throws ThingsboardException {
        try {
            TenantId tenantId = getCurrentUser().getTenantId();
            UUID id = toUUID(strId);
            
            // 查询考核表
            AssessmentTableDto dto = checkNotNull(assessmentTableService.getAssessmentTableById(tenantId, id));
            return new ResponseEntity<>(dto, HttpStatus.OK);
        } catch (Exception e) {
            log.error("根据ID获取考核表失败", e);
            throw handleException(e);
        }
    }

    @ApiOperation(value = "分页查询考核表列表", notes = "分页查询考核表列表")
    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN')")
    @GetMapping
    public ResponseEntity<PageData<AssessmentTableDto>> getAssessmentTables(
            @ApiParam(value = "页码", required = true)
            @RequestParam int page,
            @ApiParam(value = "每页记录数", required = true)
            @RequestParam int pageSize,
            @ApiParam(value = "搜索文本")
            @RequestParam(required = false) String searchText,
            @ApiParam(value = "考核表名称")
            @RequestParam(required = false) String name,
            @ApiParam(value = "区域")
            @RequestParam(required = false) String region,
            @ApiParam(value = "考核类型")
            @RequestParam(required = false) String assessmentType,
            @ApiParam(value = "考核状态")
            @RequestParam(required = false) String status,
            @ApiParam(value = "考核周期")
            @RequestParam(required = false) String period,
            @ApiParam(value = "所在分区")
            @RequestParam(required = false) String partition) throws ThingsboardException {
        try {
            TenantId tenantId = getCurrentUser().getTenantId();
            
            // 查询考核表列表
            PageData<AssessmentTableDto> pageData = assessmentTableService.getAssessmentTables(
                    tenantId, page, pageSize, searchText, name, region, assessmentType, status, period, partition);
            return new ResponseEntity<>(pageData, HttpStatus.OK);
        } catch (Exception e) {
            log.error("分页查询考核表列表失败", e);
            throw handleException(e);
        }
    }

    @ApiOperation(value = "删除考核表", notes = "根据ID删除考核表")
    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN')")
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteAssessmentTable(
            @ApiParam(value = "考核表ID", required = true)
            @PathVariable("id") String strId) throws ThingsboardException {
        try {
            TenantId tenantId = getCurrentUser().getTenantId();
            UUID id = toUUID(strId);
            
            // 删除考核表
            assessmentTableService.deleteAssessmentTable(tenantId, id);
            return new ResponseEntity<>(HttpStatus.OK);
        } catch (Exception e) {
            log.error("删除考核表失败", e);
            throw handleException(e);
        }
    }

    @ApiOperation(value = "批量删除考核表", notes = "批量删除考核表")
    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN')")
    @PostMapping("/batch-delete")
    public ResponseEntity<Void> batchDeleteAssessmentTables(
            @ApiParam(value = "考核表ID列表", required = true)
            @RequestBody List<String> strIds) throws ThingsboardException {
        try {
            TenantId tenantId = getCurrentUser().getTenantId();
            
            // 转换ID列表
            List<UUID> ids = new ArrayList<>();
            for (String strId : strIds) {
                ids.add(toUUID(strId));
            }
            
            // 批量删除考核表
            assessmentTableService.batchDeleteAssessmentTables(tenantId, ids);
            return new ResponseEntity<>(HttpStatus.OK);
        } catch (Exception e) {
            log.error("批量删除考核表失败", e);
            throw handleException(e);
        }
    }

    @ApiOperation(value = "获取考核表明细项列表", notes = "根据考核表ID获取明细项列表")
    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN')")
    @GetMapping("/{id}/items")
    public ResponseEntity<List<AssessmentTableItemDto>> getAssessmentTableItems(
            @ApiParam(value = "考核表ID", required = true)
            @PathVariable("id") String strId) throws ThingsboardException {
        try {
            TenantId tenantId = getCurrentUser().getTenantId();
            UUID id = toUUID(strId);
            
            // 查询明细项列表
            List<AssessmentTableItemDto> items = assessmentTableService.getAssessmentTableItems(tenantId, id);
            return new ResponseEntity<>(items, HttpStatus.OK);
        } catch (Exception e) {
            log.error("获取考核表明细项列表失败", e);
            throw handleException(e);
        }
    }

    @ApiOperation(value = "导入考核表", notes = "从Excel文件导入考核表")
    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN')")
    @PostMapping("/import")
    public ResponseEntity<Map<String, Object>> importAssessmentTables(
            @ApiParam(value = "Excel文件", required = true)
            @RequestParam("file") MultipartFile file) throws ThingsboardException {
        try {
            TenantId tenantId = getCurrentUser().getTenantId();
            String currentUser = getCurrentUser().getEmail();
            
            // 设置导入参数
            ImportParams importParams = new ImportParams();
            importParams.setTitleRows(1); // 标题行数
            importParams.setHeadRows(1);  // 表头行数
            importParams.setNeedVerify(true); // 是否需要校验
            
            // 导入Excel
            ExcelImportResult<AssessmentTableDto> result = ExcelImportUtil.importExcelMore(
                    file.getInputStream(), AssessmentTableDto.class, importParams);

            List<AssessmentTableDto> successList = result.getList();
            List<AssessmentTableDto> failList = new ArrayList<>();

            if (result.isVerfiyFail()) {
                // 如果有校验失败的数据，获取校验失败的数据
                failList = result.getFailList();
                log.warn("导入考核表有{}条数据校验失败", failList.size());
            }

            // 获取分区列表并构建名称到ID的映射
            String tenantIdStr = UUIDConverter.fromTimeUUID(tenantId.getId());
            Map<String, Object> params = new HashMap<>();
            params.put("tenantId", tenantIdStr);
            List<PartitionTreeDTO> partitionList = partitionService.getList(params);
            Map<String, String> partitionIdMap = buildPartitionIdMap(partitionList);
            
            // 保存成功导入的数据
            List<AssessmentTableDto> savedList = new ArrayList<>();
            int successCount = 0;
            
            for (AssessmentTableDto dto : successList) {
                try {
                    // 设置必要的字段
                    dto.setTenantId(tenantId);
                    if (dto.getId() == null || dto.getId().isEmpty()) {
                        // 新数据，设置创建人和创建时间
                        dto.setCreator(currentUser);
                        dto.setCreateTime(new Date());
                    }

                    // 将分区名称转换为分区ID
                    String partitionName = dto.getPartition();
                    if (partitionName != null && !partitionName.trim().isEmpty()) {
                        String partitionId = partitionIdMap.get(partitionName.trim());
                        if (partitionId != null) {
                            dto.setPartition(partitionId);
                        } else {
                            log.warn("未找到分区名称对应的ID: {}", partitionName);
                            // 如果找不到对应的分区ID，可以选择跳过这条记录或者保留原始名称
                            // 这里选择跳过这条记录并添加到失败列表
                            dto.setPartition(null); // 清空分区字段，避免保存错误数据
                            log.error("导入失败：未找到分区名称 '{}' 对应的分区ID", partitionName);
                            failList.add(dto);
                            continue;
                        }
                    }

                    // 保存考核表
                    AssessmentTableDto savedDto = assessmentTableService.saveAssessmentTable(dto);
                    savedList.add(savedDto);
                    successCount++;
                } catch (Exception e) {
                    log.error("保存导入的考核表失败: {}", dto.getName(), e);
                    failList.add(dto);
                }
            }
            
            // 返回导入结果
            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("success", successCount);
            resultMap.put("fail", failList.size());
            resultMap.put("total", successList.size() + failList.size());
            
            return new ResponseEntity<>(resultMap, HttpStatus.OK);
        } catch (Exception e) {
            log.error("导入考核表失败", e);
            throw handleException(e);
        }
    }

    @ApiOperation(value = "导出考核表", notes = "导出考核表为Excel文件")
    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN')")
    @GetMapping("/export")
    public void exportAssessmentTables(
            @ApiParam(value = "考核表名称")
            @RequestParam(required = false) String name,
            @ApiParam(value = "区域")
            @RequestParam(required = false) String region,
            @ApiParam(value = "考核类型")
            @RequestParam(required = false) String assessmentType,
            @ApiParam(value = "考核状态")
            @RequestParam(required = false) String status,
            @ApiParam(value = "考核周期")
            @RequestParam(required = false) String period,
            @ApiParam(value = "所在分区")
            @RequestParam(required = false) String partition,
            HttpServletResponse response) throws ThingsboardException {
        try {
            TenantId tenantId = getCurrentUser().getTenantId();

            // 查询要导出的数据
            PageData<AssessmentTableDto> pageData = assessmentTableService.getAssessmentTables(
                    tenantId, 0, Integer.MAX_VALUE, null, name, region, assessmentType, status, period, partition);

            if (pageData != null && pageData.getData() != null && !pageData.getData().isEmpty()) {
                // 获取分区列表并构建ID到名称的映射
                String tenantIdStr = UUIDConverter.fromTimeUUID(tenantId.getId());
                Map<String, Object> params = new HashMap<>();
                params.put("tenantId", tenantIdStr);
                List<PartitionTreeDTO> partitionList = partitionService.getList(params);
                Map<String, String> partitionNameMap = buildPartitionNameMap(partitionList);

                // 处理导出数据，将分区ID映射为分区名称
                for (AssessmentTableDto dto : pageData.getData()) {
                    // 将分区ID映射为分区名称
                    String partitionId = dto.getPartition();
                    String partitionName = partitionNameMap.getOrDefault(partitionId, partitionId);
                    dto.setPartition(partitionName);
                }

                // 使用EasyPoi导出Excel
                ExportParams exportParams = new ExportParams("考核表数据", "考核表");
                Workbook workbook = ExcelExportUtil.exportExcel(exportParams, AssessmentTableDto.class, pageData.getData());

                // 设置响应头
                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("考核表导出.xlsx", "UTF-8"));

                // 写入响应流
                workbook.write(response.getOutputStream());
                workbook.close();
            } else {
                // 没有数据时创建空工作簿
                ExportParams exportParams = new ExportParams("考核表数据", "考核表");
                Workbook workbook = ExcelExportUtil.exportExcel(exportParams, AssessmentTableDto.class, new ArrayList<>());

                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("考核表导出.xlsx", "UTF-8"));
                workbook.write(response.getOutputStream());
                workbook.close();
            }
        } catch (Exception e) {
            log.error("导出考核表失败", e);
            throw handleException(e);
        }
    }

    @ApiOperation(value = "下载导入模板", notes = "下载考核表导入模板")
    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN')")
    @GetMapping("/template")
    public void downloadTemplate(HttpServletResponse response) throws ThingsboardException {
        try {
            // 使用EasyPoi导出空模板
            ExportParams exportParams = new ExportParams("考核表导入模板", "考核表");
            // 创建一个空的考核表对象列表作为模板
            List<AssessmentTableDto> templateList = new ArrayList<>();
            // 可以添加一个示例数据
            AssessmentTableDto template = new AssessmentTableDto();
            template.setName("示例考核表");
            template.setRegion("示例区域");
            template.setPartition("示例分区");
            template.setPeriod("2023-06");
            template.setAssessmentType("1"); // 水质考核
            template.setAssessmentLevel("A"); // 优秀
            template.setStatus("0"); // 草稿
            template.setTotalScore(90.0);
            template.setRemark("示例备注");
            templateList.add(template);
            
            Workbook workbook = ExcelExportUtil.exportExcel(exportParams, AssessmentTableDto.class, templateList);
            
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("考核表导入模板.xlsx", "UTF-8"));
            workbook.write(response.getOutputStream());
            workbook.close();
        } catch (Exception e) {
            log.error("下载导入模板失败", e);
            throw handleException(e);
        }
    }

    /**
     * 构建分区ID到名称的映射
     * @param partitionList 分区树形列表
     * @return 分区ID到名称的映射
     */
    private Map<String, String> buildPartitionNameMap(List<PartitionTreeDTO> partitionList) {
        Map<String, String> nameMap = new HashMap<>();
        if (partitionList != null && !partitionList.isEmpty()) {
            buildPartitionNameMapRecursive(partitionList, nameMap);
        }
        return nameMap;
    }

    /**
     * 递归构建分区名称映射
     * @param partitionList 分区列表
     * @param nameMap 名称映射
     */
    private void buildPartitionNameMapRecursive(List<PartitionTreeDTO> partitionList, Map<String, String> nameMap) {
        for (PartitionTreeDTO partition : partitionList) {
            if (partition.getId() != null && partition.getName() != null) {
                nameMap.put(partition.getId(), partition.getName());
            }
            if (partition.getChildren() != null && !partition.getChildren().isEmpty()) {
                buildPartitionNameMapRecursive(partition.getChildren(), nameMap);
            }
        }
    }

    /**
     * 构建分区名称到ID的映射（用于导入）
     * @param partitionList 分区树形列表
     * @return 分区名称到ID的映射
     */
    private Map<String, String> buildPartitionIdMap(List<PartitionTreeDTO> partitionList) {
        Map<String, String> idMap = new HashMap<>();
        if (partitionList != null && !partitionList.isEmpty()) {
            buildPartitionIdMapRecursive(partitionList, idMap);
        }
        return idMap;
    }

    /**
     * 递归构建分区名称到ID的映射
     * @param partitionList 分区列表
     * @param idMap ID映射
     */
    private void buildPartitionIdMapRecursive(List<PartitionTreeDTO> partitionList, Map<String, String> idMap) {
        for (PartitionTreeDTO partition : partitionList) {
            if (partition.getId() != null && partition.getName() != null) {
                idMap.put(partition.getName(), partition.getId());
            }
            if (partition.getChildren() != null && !partition.getChildren().isEmpty()) {
                buildPartitionIdMapRecursive(partition.getChildren(), idMap);
            }
        }
    }
}