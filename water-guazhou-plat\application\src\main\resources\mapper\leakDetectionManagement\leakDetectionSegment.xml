<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.leakDetectionPlan.LeakDetectionSegmentDao">

    <!-- 根据方案ID查询探漏分段 -->
    <select id="findByPlanId" resultType="org.thingsboard.server.dao.model.sql.leakDetectionPlan.LeakDetectionSegmentEntity">
        SELECT *
        FROM leak_detection_segment
        WHERE plan_id = #{planId} AND deleted = 0
        ORDER BY created_time DESC
    </select>

    <!-- 分页查询探漏分段 -->
    <select id="querySegmentsByPage" resultType="org.thingsboard.server.dao.model.sql.leakDetectionPlan.LeakDetectionSegmentEntity">
        SELECT 
            s.*,
            p.name as planName,
            p.partition_id as partitionId,
            p.responsible_person as planResponsiblePerson
        FROM leak_detection_segment s
        LEFT JOIN leak_detection_plan p ON s.plan_id = p.id
        <where>
            s.deleted = 0
            <if test="planId != null and planId != ''">
                AND s.plan_id = #{planId}
            </if>
            <if test="name != null and name != ''">
                AND s.name LIKE concat('%', #{name}, '%')
            </if>
            <if test="segmentCode != null and segmentCode != ''">
                AND s.segment_code = #{segmentCode}
            </if>
            <if test="status != null and status != ''">
                AND s.status = #{status}
            </if>
            <if test="priority != null">
                AND s.priority = #{priority}
            </if>
        </where>
        ORDER BY p.name,s.created_time DESC
    </select>

    <!-- 根据方案ID统计探漏分段状态数量 -->
    <select id="countSegmentStatusByPlanId" resultType="java.util.Map">
        SELECT status, COUNT(1) as count
        FROM leak_detection_segment
        WHERE plan_id = #{planId} AND deleted = 0
        GROUP BY status
    </select>

    <!-- 根据方案ID删除探漏分段 -->
    <update id="deleteByPlanId">
        UPDATE leak_detection_segment
        SET deleted = 1, updated_time = NOW()
        WHERE plan_id = #{planId} AND deleted = 0
    </update>

    <!-- 逻辑删除探漏分段 -->
    <update id="deleteLeakDetectionSegment">
        UPDATE leak_detection_segment
        SET deleted = 1, updated_time = NOW()
        WHERE id = #{id} AND deleted = 0
    </update>
    
    <!-- 查询分区下可用的设备（未绑定到指定分段的设备） -->
    <select id="queryAvailableDevices" resultType="org.thingsboard.server.dao.model.DTO.DeviceDTO">
        SELECT 
            d.id as deviceId,
            d.name,
            pm.type,
            d.additional_info as additionalInfo,
            pm.partition_id as partitionId,
            pm.direction,
            pm.is_account as isAccount,
            pm.create_time as createTime
        FROM 
            tb_pipe_partition_mount pm
        JOIN 
            device d ON pm.device_id = d.id
        <where>
            pm.partition_id = #{partitionId}
            <if test="boundDeviceIds != null and boundDeviceIds.size() > 0">
                AND pm.device_id NOT IN
                <foreach collection="boundDeviceIds" item="deviceId" open="(" separator="," close=")">
                    #{deviceId}
                </foreach>
            </if>
            <if test="name != null and name != ''">
                AND d.name LIKE '%' || #{name} || '%'
            </if>
            <if test="type != null and type != ''">
                AND pm.type = #{type}
            </if>
            AND d.is_delete = '0'
        </where>
        ORDER BY pm.create_time DESC
    </select>
    
    <!-- 根据设备ID列表查询设备详情 -->
    <select id="queryDeviceDetailsByIds" resultType="org.thingsboard.server.dao.model.DTO.DeviceDTO">
        SELECT DISTINCT
            d.id as deviceId,
            d.name,
            pm.type,
            pm.direction,
            pm.is_account as isAccount,
            pm.partition_id as partitionId,
            pm.create_time as createTime,
            d.additional_info as additionalInfo
        FROM
        device d
        LEFT JOIN
        tb_pipe_partition_mount pm ON d.id = pm.device_id
        <where>
            <if test="deviceIds != null and deviceIds.size() > 0">
                d.id IN
                <foreach collection="deviceIds" item="deviceId" open="(" separator="," close=")">
                    #{deviceId}
                </foreach>
            </if>
            <if test="partitionId != null and partitionId != ''">
                AND pm.partition_id = #{partitionId}
            </if>
            AND d.is_delete = '0'
        </where>
    </select>

</mapper> 