package org.thingsboard.server.dao.util.imodel.query.deviceAuth;

import lombok.Data;
import java.util.List;

/**
 * 批量获取设备用户权限请求
 */
@Data
public class BatchDeviceUserAuthRequest {
    
    /**
     * 设备ID列表
     */
    private List<String> deviceIds;
    
    /**
     * 参数验证
     */
    public String validate() {
        if (deviceIds == null || deviceIds.isEmpty()) {
            return "设备ID列表不能为空";
        }
        
        if (deviceIds.size() > 100) {
            return "单次查询设备数量不能超过100个";
        }
        
        // 验证设备ID格式
        for (String deviceId : deviceIds) {
            if (deviceId == null || deviceId.trim().isEmpty()) {
                return "设备ID不能为空";
            }
        }
        
        return null;
    }
}
