package org.thingsboard.server.dao.remoteControl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.remoteControl.DeviceControlRecordSetting;
import org.thingsboard.server.dao.model.sql.remoteControl.RemoteControlDeviceSetting;

import java.util.List;
import java.util.Map;

/**
 * 远程设备控制服务接口
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
public interface RemoteControlService {

    // ==================== 设备控制记录相关 ====================

    /**
     * 分页查询设备控制记录
     *
     * @param page 页码
     * @param size 每页大小
     * @param deviceId 设备ID
     * @param deviceName 设备名称
     * @param controlType 控制类型
     * @param status 控制状态
     * @param operatorName 操作员姓名
     * @param tenantId 租户ID
     * @return 分页结果
     */
    IPage<DeviceControlRecordSetting> getControlRecordList(
            int page, int size, String deviceId, String deviceName,
            String controlType, String status, String operatorName, String tenantId
    );

    /**
     * 根据设备ID查询控制记录
     *
     * @param deviceId 设备ID
     * @param tenantId 租户ID
     * @return 控制记录列表
     */
    List<DeviceControlRecordSetting> getControlRecordsByDeviceId(String deviceId, String tenantId);

    /**
     * 保存设备控制记录
     *
     * @param record 控制记录
     * @return 保存结果
     */
    DeviceControlRecordSetting saveControlRecord(DeviceControlRecordSetting record);

    /**
     * 批量保存设备控制记录
     *
     * @param records 控制记录列表
     * @return 保存结果
     */
    List<DeviceControlRecordSetting> batchSaveControlRecords(List<DeviceControlRecordSetting> records);

    /**
     * 删除设备控制记录
     *
     * @param recordId 记录ID
     * @param tenantId 租户ID
     * @return 删除结果
     */
    boolean deleteControlRecord(String recordId, String tenantId);

    /**
     * 更新控制记录状态
     *
     * @param recordId 记录ID
     * @param status 新状态
     * @param tenantId 租户ID
     * @return 更新结果
     */
    boolean updateControlRecordStatus(String recordId, String status, String tenantId);

    // ==================== 远程控制设备相关 ====================

    /**
     * 分页查询远程控制设备
     *
     * @param page 页码
     * @param size 每页大小
     * @param waterPlantId 水厂ID
     * @param deviceType 设备类型
     * @param location 设备位置
     * @param tenantId 租户ID
     * @return 分页结果
     */
    IPage<RemoteControlDeviceSetting> getRemoteControlDeviceList(
            int page, int size, String waterPlantId, String deviceType, String location, String tenantId
    );

    /**
     * 根据水厂ID查询设备列表
     *
     * @param waterPlantId 水厂ID
     * @param tenantId 租户ID
     * @return 设备列表
     */
    List<RemoteControlDeviceSetting> getDevicesByWaterPlantId(String waterPlantId, String tenantId);

    /**
     * 根据设备ID查询设备详情
     *
     * @param deviceId 设备ID
     * @param tenantId 租户ID
     * @return 设备详情
     */
    RemoteControlDeviceSetting getRemoteControlDeviceById(String deviceId, String tenantId);

    /**
     * 保存远程控制设备信息
     *
     * @param device 设备信息
     * @return 保存结果
     */
    RemoteControlDeviceSetting saveRemoteControlDevice(RemoteControlDeviceSetting device);

    /**
     * 删除远程控制设备信息
     *
     * @param deviceId 设备ID
     * @param tenantId 租户ID
     * @return 删除结果
     */
    boolean deleteRemoteControlDevice(String deviceId, String tenantId);

    // ==================== 统计相关 ====================

    /**
     * 获取设备类型统计
     *
     * @param tenantId 租户ID
     * @return 设备类型统计
     */
    List<Map<String, Object>> getDeviceTypeStats(String tenantId);

    /**
     * 获取控制记录统计
     *
     * @param tenantId 租户ID
     * @return 控制记录统计
     */
    Map<String, Object> getControlRecordStats(String tenantId);
}
