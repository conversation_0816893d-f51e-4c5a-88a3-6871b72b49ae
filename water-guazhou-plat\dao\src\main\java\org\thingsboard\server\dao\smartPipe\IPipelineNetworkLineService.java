package org.thingsboard.server.dao.smartPipe;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.sql.smartPipe.PipelineNetworkLine;
import org.thingsboard.server.dao.util.imodel.query.smartPipe.PipelineNetworkLinePageRequest;

import java.util.List;

/**
 * 管网采集-管线Service接口
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
public interface IPipelineNetworkLineService {
    /**
     * 查询管网采集-管线
     *
     * @param id 管网采集-管线主键
     * @return 管网采集-管线
     */
    public PipelineNetworkLine selectPipelineNetworkLineById(String id);

    /**
     * 查询管网采集-管线列表
     *
     * @param pipelineNetworkLine 管网采集-管线
     * @return 管网采集-管线集合
     */
    public IPage<PipelineNetworkLine> selectPipelineNetworkLineList(PipelineNetworkLinePageRequest pipelineNetworkLine);

    /**
     * 新增管网采集-管线
     *
     * @param pipelineNetworkLine 管网采集-管线
     * @return 结果
     */
    public int insertPipelineNetworkLine(PipelineNetworkLine pipelineNetworkLine);

    /**
     * 修改管网采集-管线
     *
     * @param pipelineNetworkLine 管网采集-管线
     * @return 结果
     */
    public int updatePipelineNetworkLine(PipelineNetworkLine pipelineNetworkLine);

    /**
     * 批量删除管网采集-管线
     *
     * @param ids 需要删除的管网采集-管线主键集合
     * @return 结果
     */
    public int deletePipelineNetworkLineByIds(List<String> ids);

    /**
     * 删除管网采集-管线信息
     *
     * @param id 管网采集-管线主键
     * @return 结果
     */
    public int deletePipelineNetworkLineById(String id);
}
