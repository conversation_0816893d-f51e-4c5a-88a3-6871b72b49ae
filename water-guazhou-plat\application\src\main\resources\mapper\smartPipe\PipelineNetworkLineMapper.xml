<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartPipe.PipelineNetworkLineMapper">
    
    <resultMap type="org.thingsboard.server.dao.model.sql.smartPipe.PipelineNetworkLine" id="PipelineNetworkLineResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="code"    column="code"    />
        <result property="diameter"    column="diameter"    />
        <result property="material"    column="material"    />
        <result property="startDot"    column="start_dot"    />
        <result property="endDot"    column="end_dot"    />
        <result property="startElevation"    column="start_elevation"    />
        <result property="endElevation"    column="end_elevation"    />
        <result property="startDeep"    column="start_deep"    />
        <result property="endDeep"    column="end_deep"    />
        <result property="engineeringDataId"    column="engineering_data_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="tenantId"    column="tenant_id"    />
    </resultMap>

    <sql id="selectPipelineNetworkLineVo">
        select id, name, code, diameter, material, start_dot, end_dot, start_elevation, end_elevation, start_deep, end_deep, engineering_data_id, create_time, update_time, tenant_id from pipeline_network_line
    </sql>

    <select id="selectPipelineNetworkLineList" parameterType="org.thingsboard.server.dao.model.sql.smartPipe.PipelineNetworkLine" resultMap="PipelineNetworkLineResult">
        <include refid="selectPipelineNetworkLineVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="code != null  and code != ''"> and code = #{code}</if>
            <if test="diameter != null  and diameter != ''"> and diameter = #{diameter}</if>
            <if test="material != null  and material != ''"> and material = #{material}</if>
            <if test="startDot != null  and startDot != ''"> and start_dot = #{startDot}</if>
            <if test="endDot != null  and endDot != ''"> and end_dot = #{endDot}</if>
            <if test="startElevation != null  and startElevation != ''"> and start_elevation = #{startElevation}</if>
            <if test="endElevation != null  and endElevation != ''"> and end_elevation = #{endElevation}</if>
            <if test="startDeep != null  and startDeep != ''"> and start_deep = #{startDeep}</if>
            <if test="endDeep != null  and endDeep != ''"> and end_deep = #{endDeep}</if>
            <if test="engineeringDataId != null  and engineeringDataId != ''"> and engineering_data_id = #{engineeringDataId}</if>
            <if test="tenantId != null  and tenantId != ''"> and tenant_id = #{tenantId}</if>
        </where>
    </select>
    
    <select id="selectPipelineNetworkLineById" parameterType="String" resultMap="PipelineNetworkLineResult">
        <include refid="selectPipelineNetworkLineVo"/>
        where id = #{id}
    </select>

    <insert id="insertPipelineNetworkLine" parameterType="org.thingsboard.server.dao.model.sql.smartPipe.PipelineNetworkLine">
        insert into pipeline_network_line
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null">name,</if>
            <if test="code != null">code,</if>
            <if test="diameter != null">diameter,</if>
            <if test="material != null">material,</if>
            <if test="startDot != null">start_dot,</if>
            <if test="endDot != null">end_dot,</if>
            <if test="startElevation != null">start_elevation,</if>
            <if test="endElevation != null">end_elevation,</if>
            <if test="startDeep != null">start_deep,</if>
            <if test="endDeep != null">end_deep,</if>
            <if test="engineeringDataId != null">engineering_data_id,</if>
            create_time,
            update_time,
            <if test="tenantId != null">tenant_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null">#{name},</if>
            <if test="code != null">#{code},</if>
            <if test="diameter != null">#{diameter},</if>
            <if test="material != null">#{material},</if>
            <if test="startDot != null">#{startDot},</if>
            <if test="endDot != null">#{endDot},</if>
            <if test="startElevation != null">#{startElevation},</if>
            <if test="endElevation != null">#{endElevation},</if>
            <if test="startDeep != null">#{startDeep},</if>
            <if test="endDeep != null">#{endDeep},</if>
            <if test="engineeringDataId != null">#{engineeringDataId},</if>
            NOW(),
            NOW(),
            <if test="tenantId != null">#{tenantId},</if>
         </trim>
    </insert>

    <update id="updatePipelineNetworkLine" parameterType="org.thingsboard.server.dao.model.sql.smartPipe.PipelineNetworkLine">
        update pipeline_network_line
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="code != null">code = #{code},</if>
            <if test="diameter != null">diameter = #{diameter},</if>
            <if test="material != null">material = #{material},</if>
            <if test="startDot != null">start_dot = #{startDot},</if>
            <if test="endDot != null">end_dot = #{endDot},</if>
            <if test="startElevation != null">start_elevation = #{startElevation},</if>
            <if test="endElevation != null">end_elevation = #{endElevation},</if>
            <if test="startDeep != null">start_deep = #{startDeep},</if>
            <if test="endDeep != null">end_deep = #{endDeep},</if>
            <if test="engineeringDataId != null">engineering_data_id = #{engineeringDataId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            update_time = NOW(),
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePipelineNetworkLineById" parameterType="String">
        delete from pipeline_network_line where id = #{id}
    </delete>

    <delete id="deletePipelineNetworkLineByIds" parameterType="String">
        delete from pipeline_network_line where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>