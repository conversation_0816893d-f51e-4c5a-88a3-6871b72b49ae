package org.thingsboard.server.controller.smartPipe;

import java.util.List;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.thingsboard.server.common.data.UUIDConverter;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.model.sql.smartPipe.PipelineNetworkLine;
import org.thingsboard.server.dao.smartPipe.IPipelineNetworkLineService;
import org.thingsboard.server.dao.util.imodel.query.smartPipe.PipelineNetworkLinePageRequest;
import org.thingsboard.server.dao.util.imodel.response.IstarResponse;
import org.thingsboard.server.service.aspect.annotation.MonitorPerformance;

/**
 * 管网采集-管线Controller
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Api(tags = "管网采集-管线数据")
@RestController
@RequestMapping("api/pipeline/engineering/line")
public class PipelineNetworkLineController extends BaseController {

    @Autowired
    private IPipelineNetworkLineService pipelineNetworkLineService;

    /**
     * 查询管网采集-管线列表
     */
    @MonitorPerformance(description = "管网采集-查询管线数据列表")
    @ApiOperation(value = "查询管线数据列表")
    @GetMapping("/list")
    public IstarResponse list(PipelineNetworkLinePageRequest pipelineNetworkLine) {
        return IstarResponse.ok(pipelineNetworkLineService.selectPipelineNetworkLineList(pipelineNetworkLine));
    }

    /**
     * 获取管网采集-管线详细信息
     */
    @MonitorPerformance(description = "管网采集-查询管线数据详情")
    @ApiOperation(value = "查询管线数据详情")
    @GetMapping(value = "/getDetail")
    public IstarResponse getInfo(String id) {
        return IstarResponse.ok(pipelineNetworkLineService.selectPipelineNetworkLineById(id));
    }

    /**
     * 新增管网采集-管线
     */
    @MonitorPerformance(description = "管网采集-新增管线数据")
    @ApiOperation(value = "新增管线数据")
    @PostMapping("/add")
    public IstarResponse add(@RequestBody PipelineNetworkLine pipelineNetworkLine) throws ThingsboardException {
        pipelineNetworkLine.setTenantId(UUIDConverter.fromTimeUUID(getTenantId().getId()));
        return IstarResponse.ok(pipelineNetworkLineService.insertPipelineNetworkLine(pipelineNetworkLine));
    }

    /**
     * 修改管网采集-管线
     */
    @MonitorPerformance(description = "管网采集-修改管线数据")
    @ApiOperation(value = "修改管线数据")
    @PostMapping("/edit")
    public IstarResponse edit(@RequestBody PipelineNetworkLine pipelineNetworkLine) {
        return IstarResponse.ok(pipelineNetworkLineService.updatePipelineNetworkLine(pipelineNetworkLine));
    }

    /**
     * 删除管网采集-管线
     */
    @MonitorPerformance(description = "管网采集-删除管线数据")
    @ApiOperation(value = "删除管线数据")
    @DeleteMapping("/deleteIds")
    public IstarResponse remove(@RequestBody List<String> ids) {
        return IstarResponse.ok(pipelineNetworkLineService.deletePipelineNetworkLineByIds(ids));
    }
}
