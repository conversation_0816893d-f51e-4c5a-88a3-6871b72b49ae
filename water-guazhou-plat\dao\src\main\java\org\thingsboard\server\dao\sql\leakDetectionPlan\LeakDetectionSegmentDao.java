package org.thingsboard.server.dao.sql.leakDetectionPlan;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import org.thingsboard.server.dao.model.sql.leakDetectionPlan.LeakDetectionSegmentEntity;
import org.thingsboard.server.dao.model.DTO.DeviceDTO;

import java.util.List;

/**
 * 探漏分段DAO接口
 */
@Mapper
@Repository
public interface LeakDetectionSegmentDao extends BaseMapper<LeakDetectionSegmentEntity> {

    /**
     * 根据方案ID查询探漏分段
     *
     * @param planId 方案ID
     * @return 探漏分段列表
     */
    List<LeakDetectionSegmentEntity> findByPlanId(@Param("planId") String planId);

    /**
     * 分页查询探漏分段
     *
     * @param page         分页参数
     * @param planId       方案ID
     * @param name         分段名称
     * @param segmentCode  分段编码
     * @param status       状态
     * @param priority     优先级
     * @return 分页结果
     */
    Page<LeakDetectionSegmentEntity> querySegmentsByPage(Page<LeakDetectionSegmentEntity> page,
                                                       @Param("planId") String planId,
                                                       @Param("name") String name,
                                                       @Param("segmentCode") String segmentCode,
                                                       @Param("status") String status,
                                                       @Param("priority") Integer priority);

    /**
     * 根据方案ID统计探漏分段状态数量
     *
     * @param planId 方案ID
     * @return 状态统计
     */
    List<Object> countSegmentStatusByPlanId(@Param("planId") String planId);

    /**
     * 根据方案ID删除探漏分段
     *
     * @param planId 方案ID
     * @return 影响行数
     */
    int deleteByPlanId(@Param("planId") String planId);
    
    /**
     * 根据ID删除探漏分段
     *
     * @param id 分段ID
     * @return 影响行数
     */
    int deleteLeakDetectionSegment(@Param("id") String id);
    
    /**
     * 查询分区下可用的设备（未绑定到指定分段的设备）
     *
     * @param page         分页参数
     * @param partitionId  分区ID
     * @param boundDeviceIds 已绑定的设备ID列表
     * @param name         设备名称
     * @param type         设备类型
     * @return 分页结果
     */
    Page<DeviceDTO> queryAvailableDevices(Page<DeviceDTO> page,
                                        @Param("partitionId") String partitionId,
                                        @Param("boundDeviceIds") List<String> boundDeviceIds,
                                        @Param("name") String name,
                                        @Param("type") String type);
    
    /**
     * 根据设备ID列表查询设备详情
     *
     * @param deviceIds 设备ID列表
     * @param partitionId 分区ID
     * @return 设备详情列表
     */
    List<DeviceDTO> queryDeviceDetailsByIds(@Param("deviceIds") List<String> deviceIds, @Param("partitionId") String partitionId);
} 