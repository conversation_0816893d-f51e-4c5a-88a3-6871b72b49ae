package org.thingsboard.server.dao.model.DTO;

import lombok.Data;
import org.thingsboard.server.dao.model.sql.leakDetectionPlan.LeakDetectionPlanEntity;
import org.thingsboard.server.dao.model.sql.leakDetectionPlan.LeakDetectionSegmentEntity;

import java.util.List;

/**
 * 探漏方案DTO，包含探漏方案和相关的分段数据
 */
@Data
public class LeakDetectionPlanDTO {

    /**
     * 探漏方案实体
     */
    private LeakDetectionPlanEntity plan;

    /**
     * 探漏分段列表
     */
    private List<LeakDetectionSegmentEntity> segments;
} 