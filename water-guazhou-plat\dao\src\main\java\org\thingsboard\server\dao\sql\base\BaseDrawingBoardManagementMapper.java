package org.thingsboard.server.dao.sql.base;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.base.BaseDrawingBoardManagement;
import org.thingsboard.server.dao.util.imodel.query.base.BaseDrawingBoardManagementPageRequest;

import java.util.List;

/**
 * 平台管理-画板管理Mapper接口
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@Mapper
public interface BaseDrawingBoardManagementMapper {
    /**
     * 查询平台管理-画板管理
     *
     * @param id 平台管理-画板管理主键
     * @return 平台管理-画板管理
     */
    public BaseDrawingBoardManagement selectBaseDrawingBoardManagementById(String id);

    /**
     * 查询平台管理-画板管理列表
     *
     * @param baseDrawingBoardManagement 平台管理-画板管理
     * @return 平台管理-画板管理集合
     */
    public IPage<BaseDrawingBoardManagement> selectBaseDrawingBoardManagementList(BaseDrawingBoardManagementPageRequest baseDrawingBoardManagement);

    /**
     * 新增平台管理-画板管理
     *
     * @param baseDrawingBoardManagement 平台管理-画板管理
     * @return 结果
     */
    public int insertBaseDrawingBoardManagement(BaseDrawingBoardManagement baseDrawingBoardManagement);

    /**
     * 修改平台管理-画板管理
     *
     * @param baseDrawingBoardManagement 平台管理-画板管理
     * @return 结果
     */
    public int updateBaseDrawingBoardManagement(BaseDrawingBoardManagement baseDrawingBoardManagement);

    /**
     * 删除平台管理-画板管理
     *
     * @param id 平台管理-画板管理主键
     * @return 结果
     */
    public int deleteBaseDrawingBoardManagementById(String id);

    /**
     * 批量删除平台管理-画板管理
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBaseDrawingBoardManagementByIds(@Param("array") List<String> ids);

    void batchInsert(@Param("list") List<BaseDrawingBoardManagement> list);

    List<BaseDrawingBoardManagement> exportList(BaseDrawingBoardManagement request);
}
