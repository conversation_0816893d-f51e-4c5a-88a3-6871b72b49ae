package org.thingsboard.server.dao.remoteControl;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.remoteControl.DeviceControlRecordSetting;

import java.util.List;

/**
 * 设备控制记录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@Mapper
public interface DeviceControlRecordMapper extends BaseMapper<DeviceControlRecordSetting> {

    /**
     * 分页查询设备控制记录
     *
     * @param page 分页参数
     * @param deviceId 设备ID
     * @param deviceName 设备名称
     * @param controlType 控制类型
     * @param status 控制状态
     * @param operatorName 操作员姓名
     * @param tenantId 租户ID
     * @return 分页结果
     */
    IPage<DeviceControlRecordSetting> selectPageList(
            Page<DeviceControlRecordSetting> page,
            @Param("deviceId") String deviceId,
            @Param("deviceName") String deviceName,
            @Param("controlType") String controlType,
            @Param("status") String status,
            @Param("operatorName") String operatorName,
            @Param("tenantId") String tenantId
    );

    /**
     * 根据设备ID查询控制记录
     *
     * @param deviceId 设备ID
     * @param tenantId 租户ID
     * @return 控制记录列表
     */
    List<DeviceControlRecordSetting> selectByDeviceId(
            @Param("deviceId") String deviceId,
            @Param("tenantId") String tenantId
    );

    /**
     * 根据操作员ID查询控制记录
     *
     * @param operatorId 操作员ID
     * @param tenantId 租户ID
     * @return 控制记录列表
     */
    List<DeviceControlRecordSetting> selectByOperatorId(
            @Param("operatorId") String operatorId,
            @Param("tenantId") String tenantId
    );

    /**
     * 统计控制记录数量
     *
     * @param deviceId 设备ID
     * @param status 控制状态
     * @param tenantId 租户ID
     * @return 记录数量
     */
    Long countByCondition(
            @Param("deviceId") String deviceId,
            @Param("status") String status,
            @Param("tenantId") String tenantId
    );

    /**
     * 批量更新控制记录状态
     *
     * @param ids 记录ID列表
     * @param status 新状态
     * @param tenantId 租户ID
     * @return 更新数量
     */
    int batchUpdateStatus(
            @Param("ids") List<String> ids,
            @Param("status") String status,
            @Param("tenantId") String tenantId
    );
}
