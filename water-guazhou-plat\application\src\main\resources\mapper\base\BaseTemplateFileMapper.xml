<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.base.BaseTemplateFileMapper">
    
    <resultMap type="org.thingsboard.server.dao.model.sql.base.BaseTemplateFile" id="BaseTemplateFileResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="code"    column="code"    />
        <result property="description"    column="description"    />
        <result property="type"    column="type"    />
        <result property="fileType"    column="file_type"    />
        <result property="fileUrl"    column="file_url"    />
    </resultMap>

    <sql id="selectBaseTemplateFileVo">
        select id, name, code, description, type, file_type, file_url from base_template_file
    </sql>

    <select id="selectBaseTemplateFileList" parameterType="org.thingsboard.server.dao.model.sql.base.BaseTemplateFile" resultMap="BaseTemplateFileResult">
        <include refid="selectBaseTemplateFileVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="code != null  and code != ''"> and code = #{code}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="fileType != null  and fileType != ''"> and file_type = #{fileType}</if>
            <if test="fileUrl != null  and fileUrl != ''"> and file_url = #{fileUrl}</if>
        </where>
    </select>
    
    <select id="selectBaseTemplateFileById" parameterType="String" resultMap="BaseTemplateFileResult">
        <include refid="selectBaseTemplateFileVo"/>
        where id = #{id}
    </select>

    <select id="exportList" resultMap="BaseTemplateFileResult">
        <include refid="selectBaseTemplateFileVo"/>
        <where>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="code != null  and code != ''"> and code = #{code}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="fileType != null  and fileType != ''"> and file_type = #{fileType}</if>
            <if test="fileUrl != null  and fileUrl != ''"> and file_url = #{fileUrl}</if>
        </where>
    </select>

    <insert id="insertBaseTemplateFile" parameterType="org.thingsboard.server.dao.model.sql.base.BaseTemplateFile">
        insert into base_template_file
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null">name,</if>
            <if test="code != null">code,</if>
            <if test="description != null">description,</if>
            <if test="type != null">type,</if>
            <if test="fileType != null">file_type,</if>
            <if test="fileUrl != null">file_url,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null">#{name},</if>
            <if test="code != null">#{code},</if>
            <if test="description != null">#{description},</if>
            <if test="type != null">#{type},</if>
            <if test="fileType != null">#{fileType},</if>
            <if test="fileUrl != null">#{fileUrl},</if>
         </trim>
    </insert>

    <insert id="batchInsert">
        INSERT INTO base_template_file (
        id, name, code, description, type, file_type, file_url
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id}, #{item.name}, #{item.code}, #{item.description},
            #{item.type}, #{item.fileType}, #{item.fileUrl}
            )
        </foreach>
    </insert>

    <update id="updateBaseTemplateFile" parameterType="org.thingsboard.server.dao.model.sql.base.BaseTemplateFile">
        update base_template_file
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="code != null">code = #{code},</if>
            <if test="description != null">description = #{description},</if>
            <if test="type != null">type = #{type},</if>
            <if test="fileType != null">file_type = #{fileType},</if>
            <if test="fileUrl != null">file_url = #{fileUrl},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBaseTemplateFileById" parameterType="String">
        delete from base_template_file where id = #{id}
    </delete>

    <delete id="deleteBaseTemplateFileByIds" parameterType="String">
        delete from base_template_file where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>