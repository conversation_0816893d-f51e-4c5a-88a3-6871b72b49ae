package org.thingsboard.server.dao.assessmenttable;

import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;

import java.util.List;
import java.util.UUID;

/**
 * 考核表Service接口
 */
public interface AssessmentTableService {

    /**
     * 保存考核表
     *
     * @param assessmentTableDto 考核表DTO
     * @return 保存后的考核表DTO
     */
    AssessmentTableDto saveAssessmentTable(AssessmentTableDto assessmentTableDto);

    /**
     * 根据ID查询考核表
     *
     * @param tenantId 租户ID
     * @param id 考核表ID
     * @return 考核表DTO
     */
    AssessmentTableDto getAssessmentTableById(TenantId tenantId, UUID id);

    /**
     * 分页查询考核表列表
     *
     * @param tenantId 租户ID
     * @param page 页码
     * @param pageSize 每页记录数
     * @param searchText 搜索文本
     * @param name 考核表名称
     * @param region 区域
     * @param assessmentType 考核类型
     * @param status 考核状态
     * @param period 考核周期
     * @param partition 所在分区
     * @return 分页结果
     */
    PageData<AssessmentTableDto> getAssessmentTables(
            TenantId tenantId, 
            int page, 
            int pageSize, 
            String searchText,
            String name,
            String region,
            String assessmentType,
            String status,
            String period,
            String partition
    );

    /**
     * 根据ID删除考核表
     *
     * @param tenantId 租户ID
     * @param id 考核表ID
     */
    void deleteAssessmentTable(TenantId tenantId, UUID id);

    /**
     * 批量删除考核表
     *
     * @param tenantId 租户ID
     * @param ids 考核表ID列表
     */
    void batchDeleteAssessmentTables(TenantId tenantId, List<UUID> ids);

    /**
     * 根据考核表ID查询明细项列表
     *
     * @param tenantId 租户ID
     * @param assessmentTableId 考核表ID
     * @return 明细项列表
     */
    List<AssessmentTableItemDto> getAssessmentTableItems(TenantId tenantId, UUID assessmentTableId);
} 