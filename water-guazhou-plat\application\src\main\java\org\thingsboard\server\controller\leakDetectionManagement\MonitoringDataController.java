package org.thingsboard.server.controller.leakDetectionManagement;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.UUID;

/**
 * 监测数据控制器 - 使用模拟数据，后期对接真实设备数据
 */
@RestController
@RequestMapping("/api/leakDetection/monitoring")
@Api(tags = "探漏监测数据接口")
@Slf4j
public class MonitoringDataController extends BaseController {

    private final Random random = new Random();

    @ApiOperation("根据分段ID查询监测数据")
    @GetMapping("/segment/{segmentId}")
    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN', 'CUSTOMER_USER')")
    public ResponseEntity<?> getMonitoringDataBySegmentId(
            @ApiParam("分段ID") @PathVariable String segmentId,
            @ApiParam("数据量") @RequestParam(defaultValue = "10") Integer count) throws ThingsboardException {
        try {
            List<MonitoringData> dataList = generateMockMonitoringData(segmentId, null, count);
            return ResponseEntity.ok(dataList);
        } catch (Exception e) {
            log.error("Failed to get monitoring data by segment id: {}", segmentId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(e.getMessage());
        }
    }

    @ApiOperation("根据方案ID查询监测数据")
    @GetMapping("/plan/{planId}")
    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN', 'CUSTOMER_USER')")
    public ResponseEntity<?> getMonitoringDataByPlanId(
            @ApiParam("方案ID") @PathVariable String planId,
            @ApiParam("数据量") @RequestParam(defaultValue = "20") Integer count) throws ThingsboardException {
        try {
            List<MonitoringData> dataList = generateMockMonitoringData(null, planId, count);
            return ResponseEntity.ok(dataList);
        } catch (Exception e) {
            log.error("Failed to get monitoring data by plan id: {}", planId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(e.getMessage());
        }
    }

    @ApiOperation("分页查询监测数据")
    @GetMapping
    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN', 'CUSTOMER_USER')")
    public ResponseEntity<?> getMonitoringData(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam("每页记录数") @RequestParam(defaultValue = "10") Integer pageSize,
            @ApiParam("方案ID") @RequestParam(required = false) String planId,
            @ApiParam("分段ID") @RequestParam(required = false) String segmentId) throws ThingsboardException {
        try {
            // 模拟分页查询
            List<MonitoringData> allData = generateMockMonitoringData(segmentId, planId, pageSize * 3);
            
            // 简单分页
            int startIndex = (pageNum - 1) * pageSize;
            int endIndex = Math.min(startIndex + pageSize, allData.size());
            List<MonitoringData> pageData = allData.subList(startIndex, endIndex);
            
            Map<String, Object> result = new HashMap<>();
            result.put("total", allData.size());
            result.put("pageSize", pageSize);
            result.put("pageNum", pageNum);
            result.put("pages", (allData.size() + pageSize - 1) / pageSize);
            result.put("list", pageData);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("Failed to query monitoring data by page", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(e.getMessage());
        }
    }

    @ApiOperation("根据分段ID查询监测数据统计")
    @GetMapping("/statistics/{segmentId}")
    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN', 'CUSTOMER_USER')")
    public ResponseEntity<?> getSegmentMonitoringStatistics(@ApiParam("分段ID") @PathVariable String segmentId) throws ThingsboardException {
        try {
            Map<String, Object> statistics = new HashMap<>();
            
            // 模拟统计数据
            statistics.put("avgFlowRate", new BigDecimal(random.nextDouble() * 5 + 2).setScale(2, RoundingMode.HALF_UP));
            statistics.put("maxFlowRate", new BigDecimal(random.nextDouble() * 3 + 8).setScale(2, RoundingMode.HALF_UP));
            statistics.put("minFlowRate", new BigDecimal(random.nextDouble() * 1.5 + 0.5).setScale(2, RoundingMode.HALF_UP));
            statistics.put("avgPressure", new BigDecimal(random.nextDouble() * 0.2 + 0.4).setScale(2, RoundingMode.HALF_UP));
            statistics.put("maxPressure", new BigDecimal(random.nextDouble() * 0.1 + 0.65).setScale(2, RoundingMode.HALF_UP));
            statistics.put("minPressure", new BigDecimal(random.nextDouble() * 0.1 + 0.25).setScale(2, RoundingMode.HALF_UP));
            statistics.put("avgNoise", new BigDecimal(random.nextDouble() * 20 + 40).setScale(2, RoundingMode.HALF_UP));
            statistics.put("maxNoise", new BigDecimal(random.nextDouble() * 15 + 65).setScale(2, RoundingMode.HALF_UP));
            statistics.put("abnormalCount", random.nextInt(5));
            
            return ResponseEntity.ok(statistics);
        } catch (Exception e) {
            log.error("Failed to get segment monitoring statistics by segment id: {}", segmentId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(e.getMessage());
        }
    }

    @ApiOperation("根据方案ID查询异常监测数据")
    @GetMapping("/abnormal/{planId}")
    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN', 'CUSTOMER_USER')")
    public ResponseEntity<?> getAbnormalDataByPlanId(
            @ApiParam("方案ID") @PathVariable String planId,
            @ApiParam("数据量") @RequestParam(defaultValue = "5") Integer count) throws ThingsboardException {
        try {
            List<MonitoringData> allData = generateMockMonitoringData(null, planId, count * 3);
            List<MonitoringData> abnormalData = new ArrayList<>();
            
            // 筛选异常数据
            for (MonitoringData data : allData) {
                if (data.isAbnormal()) {
                    abnormalData.add(data);
                    if (abnormalData.size() >= count) {
                        break;
                    }
                }
            }
            
            // 如果异常数据不足，强制设置一些数据为异常
            if (abnormalData.size() < count) {
                for (int i = 0; i < count - abnormalData.size() && i < allData.size(); i++) {
                    MonitoringData data = allData.get(i);
                    if (!data.isAbnormal()) {
                        data.setAbnormal(true);
                        data.setAbnormalType("FLOW");
                        data.setAbnormalDescription("流量异常：" + data.getFlowRate() + " m³/h");
                        abnormalData.add(data);
                    }
                }
            }
            
            return ResponseEntity.ok(abnormalData);
        } catch (Exception e) {
            log.error("Failed to get abnormal monitoring data by plan id: {}", planId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(e.getMessage());
        }
    }

    @ApiOperation("生成模拟监测数据")
    @PostMapping("/mock")
    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN', 'CUSTOMER_USER')")
    public ResponseEntity<?> generateMockMonitoringDataApi(
            @ApiParam("分段ID") @RequestParam(required = false) String segmentId,
            @ApiParam("方案ID") @RequestParam(required = false) String planId,
            @ApiParam("生成数量") @RequestParam(defaultValue = "10") Integer count) throws ThingsboardException {
        try {
            List<MonitoringData> mockDataList = generateMockMonitoringData(segmentId, planId, count);
            return ResponseEntity.ok(mockDataList);
        } catch (Exception e) {
            log.error("Failed to generate mock monitoring data", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(e.getMessage());
        }
    }

    /**
     * 生成模拟监测数据
     * 
     * @param segmentId 分段ID
     * @param planId    方案ID
     * @param count     生成数量
     * @return 生成的监测数据列表
     */
    private List<MonitoringData> generateMockMonitoringData(String segmentId, String planId, int count) {
        List<MonitoringData> dataList = new ArrayList<>();
        
        Date now = new Date();
        
        for (int i = 0; i < count; i++) {
            MonitoringData data = new MonitoringData();
            data.setId(UUID.randomUUID().toString());
            data.setSegmentId(segmentId != null ? segmentId : UUID.randomUUID().toString());
            data.setPlanId(planId != null ? planId : UUID.randomUUID().toString());
            data.setPosition("监测点" + (i + 1));
            
            // 设置监测时间为当前时间前的随机时间（最多前30天）
            Date monitoringTime = new Date(now.getTime() - random.nextInt(30 * 24 * 60 * 60 * 1000));
            data.setMonitoringTime(monitoringTime);
            
            // 随机生成流量数据 (0.5-10 m³/h)
            data.setFlowRate(new BigDecimal(0.5 + random.nextDouble() * 9.5).setScale(2, RoundingMode.HALF_UP));
            
            // 随机生成压力数据 (0.2-0.8 MPa)
            data.setPressure(new BigDecimal(0.2 + random.nextDouble() * 0.6).setScale(2, RoundingMode.HALF_UP));
            
            // 随机生成噪声数据 (30-80 dB)
            data.setNoise(new BigDecimal(30 + random.nextDouble() * 50).setScale(2, RoundingMode.HALF_UP));
            
            // 随机生成水温数据 (5-25 °C)
            data.setTemperature(new BigDecimal(5 + random.nextDouble() * 20).setScale(2, RoundingMode.HALF_UP));
            
            // 设置设备信息
            data.setDeviceId("device-" + (1000 + random.nextInt(9000)));
            data.setDeviceName("监测设备" + (i + 1));
            data.setDeviceType(random.nextBoolean() ? "流量计" : "压力计");
            
            data.setCreatedTime(new Date());
            
            // 异常检测
            detectAbnormal(data);
            
            dataList.add(data);
        }
        
        return dataList;
    }
    
    /**
     * 异常检测
     * 
     * @param data 监测数据
     */
    private void detectAbnormal(MonitoringData data) {
        // 异常阈值设置
        final BigDecimal FLOW_RATE_MAX = new BigDecimal("8.0");
        final BigDecimal FLOW_RATE_MIN = new BigDecimal("1.0");
        final BigDecimal PRESSURE_MAX = new BigDecimal("0.7");
        final BigDecimal PRESSURE_MIN = new BigDecimal("0.3");
        final BigDecimal NOISE_MAX = new BigDecimal("70.0");
        final BigDecimal TEMPERATURE_MAX = new BigDecimal("20.0");
        final BigDecimal TEMPERATURE_MIN = new BigDecimal("8.0");
        
        boolean isAbnormal = false;
        String abnormalType = null;
        String abnormalDescription = "";
        
        // 流量异常检测
        if (data.getFlowRate().compareTo(FLOW_RATE_MAX) > 0 || data.getFlowRate().compareTo(FLOW_RATE_MIN) < 0) {
            isAbnormal = true;
            abnormalType = "FLOW";
            abnormalDescription = "流量异常：" + data.getFlowRate() + " m³/h";
        }
        
        // 压力异常检测
        if (data.getPressure().compareTo(PRESSURE_MAX) > 0 || data.getPressure().compareTo(PRESSURE_MIN) < 0) {
            isAbnormal = true;
            abnormalType = abnormalType == null ? "PRESSURE" : abnormalType + ",PRESSURE";
            abnormalDescription = abnormalDescription.isEmpty() ? 
                    "压力异常：" + data.getPressure() + " MPa" : 
                    abnormalDescription + "；压力异常：" + data.getPressure() + " MPa";
        }
        
        // 噪声异常检测
        if (data.getNoise().compareTo(NOISE_MAX) > 0) {
            isAbnormal = true;
            abnormalType = abnormalType == null ? "NOISE" : abnormalType + ",NOISE";
            abnormalDescription = abnormalDescription.isEmpty() ? 
                    "噪声异常：" + data.getNoise() + " dB" : 
                    abnormalDescription + "；噪声异常：" + data.getNoise() + " dB";
        }
        
        // 水温异常检测
        if (data.getTemperature().compareTo(TEMPERATURE_MAX) > 0 || data.getTemperature().compareTo(TEMPERATURE_MIN) < 0) {
            isAbnormal = true;
            abnormalType = abnormalType == null ? "TEMPERATURE" : abnormalType + ",TEMPERATURE";
            abnormalDescription = abnormalDescription.isEmpty() ? 
                    "水温异常：" + data.getTemperature() + " °C" : 
                    abnormalDescription + "；水温异常：" + data.getTemperature() + " °C";
        }
        
        data.setAbnormal(isAbnormal);
        data.setAbnormalType(abnormalType);
        data.setAbnormalDescription(abnormalDescription);
    }
    
    /**
     * 监测数据模型类
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MonitoringData {
        private String id;
        private String segmentId;
        private String planId;
        private String position;
        private Date monitoringTime;
        private BigDecimal flowRate;
        private BigDecimal pressure;
        private BigDecimal noise;
        private BigDecimal temperature;
        private boolean abnormal;
        private String abnormalType;
        private String abnormalDescription;
        private String deviceId;
        private String deviceName;
        private String deviceType;
        private Date createdTime;
    }
} 