<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.remoteControl.DeviceControlRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.thingsboard.server.dao.model.sql.remoteControl.DeviceControlRecordSetting">
        <id column="id" property="id" />
        <result column="device_id" property="deviceId" />
        <result column="device_name" property="deviceName" />
        <result column="device_code" property="deviceCode" />
        <result column="control_type" property="controlType" />
        <result column="control_value" property="controlValue" />
        <result column="control_description" property="controlDescription" />
        <result column="operator_id" property="operatorId" />
        <result column="operator_name" property="operatorName" />
        <result column="control_time" property="controlTime" />
        <result column="status" property="status" />
        <result column="remark" property="remark" />
        <result column="tenant_id" property="tenantId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, device_id, device_name, device_code, control_type, control_value, 
        control_description, operator_id, operator_name, control_time, status, 
        remark, tenant_id, create_time, update_time
    </sql>

    <!-- 分页查询设备控制记录 -->
    <select id="selectPageList" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM device_control_record_setting
        WHERE tenant_id = #{tenantId}
        <if test="deviceId != null and deviceId != ''">
            AND device_id = #{deviceId}
        </if>
        <if test="deviceName != null and deviceName != ''">
            AND device_name LIKE CONCAT('%', #{deviceName}, '%')
        </if>
        <if test="controlType != null and controlType != ''">
            AND control_type = #{controlType}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
        <if test="operatorName != null and operatorName != ''">
            AND operator_name LIKE CONCAT('%', #{operatorName}, '%')
        </if>
        ORDER BY control_time DESC, create_time DESC
    </select>

    <!-- 根据设备ID查询控制记录 -->
    <select id="selectByDeviceId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM device_control_record_setting
        WHERE device_id = #{deviceId} AND tenant_id = #{tenantId}
        ORDER BY control_time DESC, create_time DESC
    </select>

    <!-- 根据操作员ID查询控制记录 -->
    <select id="selectByOperatorId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM device_control_record_setting
        WHERE operator_id = #{operatorId} AND tenant_id = #{tenantId}
        ORDER BY control_time DESC, create_time DESC
    </select>

    <!-- 统计控制记录数量 -->
    <select id="countByCondition" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM device_control_record_setting
        WHERE tenant_id = #{tenantId}
        <if test="deviceId != null and deviceId != ''">
            AND device_id = #{deviceId}
        </if>
        <if test="status != null and status != ''">
            AND status = #{status}
        </if>
    </select>

    <!-- 批量更新控制记录状态 -->
    <update id="batchUpdateStatus">
        UPDATE device_control_record_setting
        SET status = #{status}, update_time = NOW()
        WHERE tenant_id = #{tenantId}
        AND id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

</mapper>
