package org.thingsboard.server.dao.model.sql.assessmenttable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.thingsboard.server.dao.assessmenttable.AssessmentTableItemDto;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * 考核表明细项实体类
 */
@Data
@Entity
@Table(name = "assessment_table_item")
@TableName("assessment_table_item")
@NoArgsConstructor
@AllArgsConstructor
public class AssessmentTableItemEntity {

    /**
     * id
     */
    @TableId(type = IdType.INPUT)
    @Column(name = "id")
    @Id
    private String id;

    /**
     * 考核表ID
     */
    @Column(name = "assessment_table_id")
    private String assessmentTableId;

    /**
     * 指标名称
     */
    @Column(name = "indicator_name")
    private String indicatorName;

    /**
     * 指标描述
     */
    @Column(name = "indicator_desc")
    private String indicatorDesc;

    /**
     * 指标类型
     * 1: 定量指标
     * 2: 定性指标
     */
    @Column(name = "indicator_type")
    private String indicatorType;

    /**
     * 指标单位
     */
    @Column(name = "indicator_unit")
    private String indicatorUnit;

    /**
     * 指标权重
     */
    @Column(name = "weight")
    private Double weight;

    /**
     * 指标目标值
     */
    @Column(name = "target_value")
    private Double targetValue;

    /**
     * 指标实际值
     */
    @Column(name = "actual_value")
    private Double actualValue;

    /**
     * 得分
     */
    @Column(name = "score")
    private Double score;

    /**
     * 评分人
     */
    @Column(name = "scorer")
    private String scorer;

    /**
     * 评分时间
     */
    @Column(name = "score_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date scoreTime;

    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 排序号
     */
    @Column(name = "sort_num")
    private Integer sortNum;

    public AssessmentTableItemEntity(AssessmentTableItemDto itemDto) {
        if (itemDto.getId() != null) {
            this.id = itemDto.getId();
        }
        this.assessmentTableId = itemDto.getAssessmentTableId();
        this.indicatorName = itemDto.getIndicatorName();
        this.indicatorDesc = itemDto.getIndicatorDesc();
        this.indicatorType = itemDto.getIndicatorType();
        this.indicatorUnit = itemDto.getIndicatorUnit();
        this.weight = itemDto.getWeight();
        this.targetValue = itemDto.getTargetValue();
        this.actualValue = itemDto.getActualValue();
        this.score = itemDto.getScore();
        this.scorer = itemDto.getScorer();
        this.scoreTime = itemDto.getScoreTime();
        this.remark = itemDto.getRemark();
        this.createTime = itemDto.getCreateTime();
        this.sortNum = itemDto.getSortNum();
    }

    public AssessmentTableItemDto toData() {
        AssessmentTableItemDto dto = new AssessmentTableItemDto();
        dto.setId(id);
        dto.setAssessmentTableId(assessmentTableId);
        dto.setIndicatorName(indicatorName);
        dto.setIndicatorDesc(indicatorDesc);
        dto.setIndicatorType(indicatorType);
        dto.setIndicatorUnit(indicatorUnit);
        dto.setWeight(weight);
        dto.setTargetValue(targetValue);
        dto.setActualValue(actualValue);
        dto.setScore(score);
        dto.setScorer(scorer);
        dto.setScoreTime(scoreTime);
        dto.setRemark(remark);
        dto.setCreateTime(createTime);
        dto.setSortNum(sortNum);
        return dto;
    }
} 