package org.thingsboard.server.dao.model.DTO.remoteControl;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 设备控制记录DTO
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@Data
@ApiModel(description = "设备控制记录")
public class DeviceControlRecordDto {

    @ApiModelProperty(value = "记录ID")
    private String id;

    @ApiModelProperty(value = "设备ID", required = true)
    private String deviceId;

    @ApiModelProperty(value = "设备名称", required = true)
    private String deviceName;

    @ApiModelProperty(value = "设备编码", required = true)
    private String deviceCode;

    @ApiModelProperty(value = "控制类型", required = true, notes = "start/stop/open/close/adjust/reset")
    private String controlType;

    @ApiModelProperty(value = "控制值")
    private String controlValue;

    @ApiModelProperty(value = "控制描述", required = true)
    private String controlDescription;

    @ApiModelProperty(value = "操作员ID")
    private String operatorId;

    @ApiModelProperty(value = "操作员姓名")
    private String operatorName;

    @ApiModelProperty(value = "控制时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date controlTime;

    @ApiModelProperty(value = "控制状态", notes = "pending/success/failed/cancelled")
    private String status;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @ApiModelProperty(value = "控制密钥", notes = "仅用于接收前端数据，不存储到数据库")
    private String controlKey;
}
