package org.thingsboard.server.dao.model.sql.remoteControl;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 设备控制记录实体类
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@Data
@TableName("device_control_record_setting")
public class DeviceControlRecordSetting {

    /**
     * 主键ID
     */
    @TableId
    private String id;

    /**
     * 设备ID
     */
    private String deviceId;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 设备编码
     */
    private String deviceCode;

    /**
     * 控制类型(start/stop/open/close/adjust/reset)
     */
    private String controlType;

    /**
     * 控制值
     */
    private String controlValue;

    /**
     * 控制描述
     */
    private String controlDescription;

    /**
     * 操作员ID
     */
    private String operatorId;

    /**
     * 操作员姓名
     */
    private String operatorName;

    /**
     * 控制时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date controlTime;

    /**
     * 控制状态(pending/success/failed/cancelled)
     */
    private String status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}
