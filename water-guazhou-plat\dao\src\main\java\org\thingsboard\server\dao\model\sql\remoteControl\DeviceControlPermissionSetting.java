package org.thingsboard.server.dao.model.sql.remoteControl;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 设备控制权限配置实体类
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@Data
@TableName("device_control_permission_setting")
public class DeviceControlPermissionSetting {

    /**
     * 主键ID
     */
    @TableId
    private String id;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 设备ID
     */
    private String deviceId;

    /**
     * 允许的控制类型(逗号分隔)
     */
    private String controlTypes;

    /**
     * 是否启用(0:禁用,1:启用)
     */
    private Integer isEnabled;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}
