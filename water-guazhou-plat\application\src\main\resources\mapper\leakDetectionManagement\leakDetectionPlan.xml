<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.leakDetectionPlan.LeakDetectionPlanDao">

    <!-- 分页查询探漏方案 -->
    <select id="queryPlansByPage" resultType="org.thingsboard.server.dao.model.sql.leakDetectionPlan.LeakDetectionPlanEntity">
        SELECT *
        FROM leak_detection_plan
        <where>
            deleted = 0
            <if test="name != null and name != ''">
                AND name LIKE concat('%', #{name}, '%')
            </if>
            <if test="areaCode != null and areaCode != ''">
                AND area_code = #{areaCode}
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="startTime != null and startTime != '' and endTime != null and endTime != ''">
                AND created_time BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
        ORDER BY created_time DESC
    </select>

    <!-- 根据区域编码查询探漏方案 -->
    <select id="findByAreaCode" resultType="org.thingsboard.server.dao.model.sql.leakDetectionPlan.LeakDetectionPlanEntity">
        SELECT *
        FROM leak_detection_plan
        WHERE area_code = #{areaCode} AND deleted = 0
        ORDER BY created_time DESC
    </select>

    <!-- 根据状态查询探漏方案 -->
    <select id="findByStatus" resultType="org.thingsboard.server.dao.model.sql.leakDetectionPlan.LeakDetectionPlanEntity">
        SELECT *
        FROM leak_detection_plan
        WHERE status = #{status} AND deleted = 0
        ORDER BY created_time DESC
    </select>

    <!-- 逻辑删除探漏方案 -->
    <update id="deleteLeakDetectionPlan">
        UPDATE leak_detection_plan
        SET deleted = 1, updated_time = now()
        WHERE id = #{id} AND deleted = 0
    </update>

</mapper> 