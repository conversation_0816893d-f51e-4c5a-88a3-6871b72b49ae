package org.thingsboard.server.dao.leakDetectionPlan;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.thingsboard.server.dao.model.DTO.LeakDetectionPlanDTO;
import org.thingsboard.server.dao.model.sql.leakDetectionPlan.LeakDetectionPlanEntity;

import java.util.List;

/**
 * 探漏方案服务接口
 */
public interface LeakDetectionPlanService {

    /**
     * 保存或更新探漏方案
     *
     * @param plan 探漏方案实体
     * @return 保存后的探漏方案
     */
    LeakDetectionPlanEntity saveOrUpdatePlan(LeakDetectionPlanEntity plan);

    /**
     * 保存或更新探漏方案及其分段数据
     *
     * @param planDTO 探漏方案DTO，包含方案和分段数据
     * @return 保存后的探漏方案DTO
     */
    LeakDetectionPlanDTO saveOrUpdatePlanWithSegments(LeakDetectionPlanDTO planDTO);

    /**
     * 根据ID查询探漏方案
     *
     * @param id 探漏方案ID
     * @return 探漏方案
     */
    LeakDetectionPlanEntity getPlanById(String id);

    /**
     * 根据ID查询探漏方案及其分段数据
     *
     * @param id 探漏方案ID
     * @return 探漏方案DTO，包含方案和分段数据
     */
    LeakDetectionPlanDTO getPlanWithSegmentsById(String id);

    /**
     * 分页查询探漏方案
     *
     * @param page        分页参数
     * @param name        方案名称
     * @param areaCode    区域编码
     * @param partitionId 分区ID
     * @param status      状态
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @return 分页结果
     */
    Page<LeakDetectionPlanEntity> queryPlansByPage(Page<LeakDetectionPlanEntity> page,
                                                 String name,
                                                 String areaCode,
                                                 String partitionId,
                                                 String status,
                                                 String startTime,
                                                 String endTime);

    /**
     * 根据区域编码查询探漏方案
     *
     * @param areaCode 区域编码
     * @return 探漏方案列表
     */
    List<LeakDetectionPlanEntity> findByAreaCode(String areaCode);

    /**
     * 根据分区ID查询探漏方案
     *
     * @param partitionId 分区ID
     * @return 探漏方案列表
     */
    List<LeakDetectionPlanEntity> findByPartitionId(String partitionId);

    /**
     * 根据状态查询探漏方案
     *
     * @param status 状态
     * @return 探漏方案列表
     */
    List<LeakDetectionPlanEntity> findByStatus(String status);

    /**
     * 删除探漏方案
     *
     * @param id 探漏方案ID
     * @return 是否删除成功
     */
    boolean deletePlan(String id);

    /**
     * 删除探漏方案及其关联的分段数据
     *
     * @param id 探漏方案ID
     * @return 是否删除成功
     */
    boolean deletePlanWithSegments(String id);

    /**
     * 批量删除探漏方案
     *
     * @param ids 探漏方案ID集合
     * @return 是否删除成功
     */
    boolean batchDeletePlans(List<String> ids);

    /**
     * 更新探漏方案状态
     *
     * @param id     探漏方案ID
     * @param status 状态
     * @return 是否更新成功
     */
    boolean updatePlanStatus(String id, String status);
} 