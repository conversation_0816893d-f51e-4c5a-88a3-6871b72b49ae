package org.thingsboard.server.dao.sql.smartPipe;


import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.thingsboard.server.dao.model.sql.smartPipe.PipelineNetworkEngineeringData;
import org.thingsboard.server.dao.util.imodel.query.smartPipe.PipelineNetworkEngineeringDataPageRequest;

import java.util.List;

/**
 * 管网采集-工程数据Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Mapper
public interface PipelineNetworkEngineeringDataMapper {
    /**
     * 查询管网采集-工程数据
     *
     * @param id 管网采集-工程数据主键
     * @return 管网采集-工程数据
     */
    public PipelineNetworkEngineeringData selectPipelineNetworkEngineeringDataById(String id);

    /**
     * 查询管网采集-工程数据列表
     *
     * @param pipelineNetworkEngineeringData 管网采集-工程数据
     * @return 管网采集-工程数据集合
     */
    public IPage<PipelineNetworkEngineeringData> selectPipelineNetworkEngineeringDataList(PipelineNetworkEngineeringDataPageRequest pipelineNetworkEngineeringData);

    /**
     * 新增管网采集-工程数据
     *
     * @param pipelineNetworkEngineeringData 管网采集-工程数据
     * @return 结果
     */
    public int insertPipelineNetworkEngineeringData(PipelineNetworkEngineeringData pipelineNetworkEngineeringData);

    /**
     * 修改管网采集-工程数据
     *
     * @param pipelineNetworkEngineeringData 管网采集-工程数据
     * @return 结果
     */
    public int updatePipelineNetworkEngineeringData(PipelineNetworkEngineeringData pipelineNetworkEngineeringData);

    /**
     * 删除管网采集-工程数据
     *
     * @param id 管网采集-工程数据主键
     * @return 结果
     */
    public int deletePipelineNetworkEngineeringDataById(String id);

    /**
     * 批量删除管网采集-工程数据
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deletePipelineNetworkEngineeringDataByIds(@Param("array") List<String> ids);
}
