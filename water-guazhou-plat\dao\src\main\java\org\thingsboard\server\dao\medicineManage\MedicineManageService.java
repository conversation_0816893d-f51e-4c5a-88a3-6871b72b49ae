package org.thingsboard.server.dao.medicineManage;

import com.alibaba.fastjson.JSONObject;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.MedicineManage;

import java.util.List;

public interface MedicineManageService {
    MedicineManage save(MedicineManage medicineManage);

    PageData<MedicineManage> list(int page, int size, String stationId);

    List<MedicineManage> findAll(String stationId);

    List<String> typeList(TenantId tenantId);

    List countByType(Long startTime, Long endTime, TenantId tenantId);

    List<JSONObject> yearReport(TenantId tenantId);

    List<MedicineManage> findByTimeAndStationId(Long startTime, Long endTime, String stationId);

    void remove(List<String> ids);
}
