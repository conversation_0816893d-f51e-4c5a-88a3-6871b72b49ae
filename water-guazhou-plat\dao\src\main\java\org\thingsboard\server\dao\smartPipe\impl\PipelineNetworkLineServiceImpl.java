package org.thingsboard.server.dao.smartPipe.impl;

import java.util.List;
import java.util.UUID;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.smartPipe.PipelineNetworkLine;
import org.thingsboard.server.dao.smartPipe.IPipelineNetworkLineService;
import org.thingsboard.server.dao.sql.smartPipe.PipelineNetworkLineMapper;
import org.thingsboard.server.dao.util.imodel.query.smartPipe.PipelineNetworkLinePageRequest;

/**
 * 管网采集-管线Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Service
public class PipelineNetworkLineServiceImpl implements IPipelineNetworkLineService {

    @Autowired
    private PipelineNetworkLineMapper pipelineNetworkLineMapper;

    /**
     * 查询管网采集-管线
     *
     * @param id 管网采集-管线主键
     * @return 管网采集-管线
     */
    @Override
    public PipelineNetworkLine selectPipelineNetworkLineById(String id) {
        return pipelineNetworkLineMapper.selectPipelineNetworkLineById(id);
    }

    /**
     * 查询管网采集-管线列表
     *
     * @param pipelineNetworkLine 管网采集-管线
     * @return 管网采集-管线
     */
    @Override
    public IPage<PipelineNetworkLine> selectPipelineNetworkLineList(PipelineNetworkLinePageRequest pipelineNetworkLine) {
        return pipelineNetworkLineMapper.selectPipelineNetworkLineList(pipelineNetworkLine);
    }

    /**
     * 新增管网采集-管线
     *
     * @param pipelineNetworkLine 管网采集-管线
     * @return 结果
     */
    @Override
    public int insertPipelineNetworkLine(PipelineNetworkLine pipelineNetworkLine) {
        pipelineNetworkLine.setId(UUID.randomUUID().toString().replace("-", ""));
        return pipelineNetworkLineMapper.insertPipelineNetworkLine(pipelineNetworkLine);
    }

    /**
     * 修改管网采集-管线
     *
     * @param pipelineNetworkLine 管网采集-管线
     * @return 结果
     */
    @Override
    public int updatePipelineNetworkLine(PipelineNetworkLine pipelineNetworkLine) {
        return pipelineNetworkLineMapper.updatePipelineNetworkLine(pipelineNetworkLine);
    }

    /**
     * 批量删除管网采集-管线
     *
     * @param ids 需要删除的管网采集-管线主键
     * @return 结果
     */
    @Override
    public int deletePipelineNetworkLineByIds(List<String> ids) {
        return pipelineNetworkLineMapper.deletePipelineNetworkLineByIds(ids);
    }

    /**
     * 删除管网采集-管线信息
     *
     * @param id 管网采集-管线主键
     * @return 结果
     */
    @Override
    public int deletePipelineNetworkLineById(String id) {
        return pipelineNetworkLineMapper.deletePipelineNetworkLineById(id);
    }
}
