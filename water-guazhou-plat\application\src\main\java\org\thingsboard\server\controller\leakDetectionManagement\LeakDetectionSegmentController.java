package org.thingsboard.server.controller.leakDetectionManagement;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.leakDetectionPlan.LeakDetectionPlanService;
import org.thingsboard.server.dao.leakDetectionPlan.LeakDetectionSegmentService;
import org.thingsboard.server.dao.model.DTO.DeviceDTO;
import org.thingsboard.server.dao.model.sql.leakDetectionPlan.LeakDetectionPlanEntity;
import org.thingsboard.server.dao.model.sql.leakDetectionPlan.LeakDetectionSegmentEntity;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

/**
 * 探漏分段控制器
 */
@RestController
@RequestMapping("/api/leakDetection/segment")
@Api(tags = "探漏分段管理接口")
@Slf4j
public class LeakDetectionSegmentController extends BaseController {

    @Autowired
    private LeakDetectionSegmentService leakDetectionSegmentService;

    @Autowired
    private LeakDetectionPlanService leakDetectionPlanService;

    @ApiOperation("保存探漏分段")
    @PostMapping
    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN', 'CUSTOMER_USER')")
    public ResponseEntity<?> saveSegment(@RequestBody LeakDetectionSegmentEntity segment) throws ThingsboardException {
        try {
            segment = leakDetectionSegmentService.saveOrUpdateSegment(segment);
            return ResponseEntity.ok(segment);
        } catch (Exception e) {
            log.error("Failed to save leak detection segment", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(e.getMessage());
        }
    }

    @ApiOperation("获取探漏分段详情")
    @GetMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN', 'CUSTOMER_USER')")
    public ResponseEntity<?> getSegmentById(@ApiParam("探漏分段ID") @PathVariable String id) throws ThingsboardException {
        try {
            LeakDetectionSegmentEntity segment = leakDetectionSegmentService.getSegmentById(id);
            if (segment == null) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body("探漏分段不存在");
            }
            return ResponseEntity.ok(segment);
        } catch (Exception e) {
            log.error("Failed to get leak detection segment by id: {}", id, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(e.getMessage());
        }
    }

    @ApiOperation("根据方案ID查询探漏分段")
    @GetMapping("/plan/{planId}")
    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN', 'CUSTOMER_USER')")
    public ResponseEntity<?> getSegmentsByPlanId(@ApiParam("方案ID") @PathVariable String planId) throws ThingsboardException {
        try {
            List<LeakDetectionSegmentEntity> segments = leakDetectionSegmentService.findByPlanId(planId);
            return ResponseEntity.ok(segments);
        } catch (Exception e) {
            log.error("Failed to get leak detection segments by plan id: {}", planId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(e.getMessage());
        }
    }

    @ApiOperation("分页查询探漏分段")
    @GetMapping
    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN', 'CUSTOMER_USER')")
    public ResponseEntity<?> getSegments(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam("每页记录数") @RequestParam(defaultValue = "10") Integer pageSize,
            @ApiParam("方案ID") @RequestParam(required = false) String planId,
            @ApiParam("分段名称") @RequestParam(required = false) String name,
            @ApiParam("分段编码") @RequestParam(required = false) String segmentCode,
            @ApiParam("状态") @RequestParam(required = false) String status,
            @ApiParam("优先级") @RequestParam(required = false) Integer priority) throws ThingsboardException {
        try {
            com.baomidou.mybatisplus.extension.plugins.pagination.Page<LeakDetectionSegmentEntity> page = 
                    new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(pageNum, pageSize);
            
            page = leakDetectionSegmentService.querySegmentsByPage(page, planId, name, segmentCode, status, priority);
            
            Map<String, Object> result = new HashMap<>();
            result.put("total", page.getTotal());
            result.put("pageSize", page.getSize());
            result.put("pageNum", page.getCurrent());
            result.put("pages", page.getPages());
            result.put("list", page.getRecords());
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("Failed to query leak detection segments by page", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(e.getMessage());
        }
    }

    @ApiOperation("删除探漏分段")
    @DeleteMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN', 'CUSTOMER_USER')")
    public ResponseEntity<?> deleteSegment(@ApiParam("探漏分段ID") @PathVariable String id) throws ThingsboardException {
        try {
            boolean result = leakDetectionSegmentService.deleteSegment(id);
            if (!result) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body("探漏分段不存在或删除失败");
            }
            return ResponseEntity.ok("删除成功");
        } catch (Exception e) {
            log.error("Failed to delete leak detection segment by id: {}", id, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(e.getMessage());
        }
    }

    @ApiOperation("批量删除探漏分段")
    @DeleteMapping("/batch")
    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN', 'CUSTOMER_USER')")
    public ResponseEntity<?> batchDeleteSegments(@ApiParam("探漏分段ID集合") @RequestBody List<String> ids) throws ThingsboardException {
        try {
            boolean result = leakDetectionSegmentService.batchDeleteSegments(ids);
            if (!result) {
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body("批量删除失败");
            }
            return ResponseEntity.ok("批量删除成功");
        } catch (Exception e) {
            log.error("Failed to batch delete leak detection segments", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(e.getMessage());
        }
    }

    @ApiOperation("更新探漏分段状态")
    @PutMapping("/{id}/status")
    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN', 'CUSTOMER_USER')")
    public ResponseEntity<?> updateSegmentStatus(
            @ApiParam("探漏分段ID") @PathVariable String id,
            @ApiParam("状态") @RequestParam String status) throws ThingsboardException {
        try {
            boolean result = leakDetectionSegmentService.updateSegmentStatus(id, status);
            if (!result) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body("探漏分段不存在或状态更新失败");
            }
            return ResponseEntity.ok("状态更新成功");
        } catch (Exception e) {
            log.error("Failed to update leak detection segment status, id: {}, status: {}", id, status, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(e.getMessage());
        }
    }

    @ApiOperation("根据方案ID统计探漏分段状态")
    @GetMapping("/statistics/{planId}")
    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN', 'CUSTOMER_USER')")
    public ResponseEntity<?> countSegmentStatusByPlanId(@ApiParam("方案ID") @PathVariable String planId) throws ThingsboardException {
        try {
            List<Map<String, Object>> statistics = leakDetectionSegmentService.countSegmentStatusByPlanId(planId);
            return ResponseEntity.ok(statistics);
        } catch (Exception e) {
            log.error("Failed to count leak detection segment status by plan id: {}", planId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(e.getMessage());
        }
    }
    
    @ApiOperation("绑定设备到探漏分段")
    @PostMapping("/{id}/devices")
    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN', 'CUSTOMER_USER')")
    public ResponseEntity<?> bindDevices(
            @ApiParam("探漏分段ID") @PathVariable String id,
            @ApiParam("设备ID列表") @RequestBody List<String> deviceIds) throws ThingsboardException {
        try {
            log.info("绑定设备到探漏分段，分段ID: {}，设备ID列表: {}", id, deviceIds);
            
            // 获取当前分段信息
            LeakDetectionSegmentEntity segment = leakDetectionSegmentService.getSegmentById(id);
            if (segment == null) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body("探漏分段不存在");
            }
            
            // 获取已绑定的设备ID字符串
            String existingDeviceIdsStr = segment.getDeviceIds();
            List<String> existingDeviceIds = new ArrayList<>();
            
            // 如果已有设备ID，将其转换为列表
            if (existingDeviceIdsStr != null && !existingDeviceIdsStr.isEmpty()) {
                existingDeviceIds = new ArrayList<>(Arrays.asList(existingDeviceIdsStr.split(",")));
            }
            
            // 合并设备ID列表（去重）
            for (String deviceId : deviceIds) {
                if (!existingDeviceIds.contains(deviceId)) {
                    existingDeviceIds.add(deviceId);
                }
            }
            
            // 将设备ID列表转换为逗号分隔的字符串
            String newDeviceIdsStr = String.join(",", existingDeviceIds);
            
            // 更新设备绑定关系
            LambdaUpdateWrapper<LeakDetectionSegmentEntity> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(LeakDetectionSegmentEntity::getId, id)
                    .set(LeakDetectionSegmentEntity::getDeviceIds, newDeviceIdsStr);
            boolean result = leakDetectionSegmentService.update(updateWrapper);
            
            if (!result) {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("设备绑定失败");
            }
            
            // 创建响应数据
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "设备绑定成功");
            response.put("deviceIds", newDeviceIdsStr);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Failed to bind devices to segment, id: {}", id, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(e.getMessage());
        }
    }
    
    @ApiOperation("解绑探漏分段的设备")
    @DeleteMapping("/{id}/devices")
    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN', 'CUSTOMER_USER')")
    public ResponseEntity<?> unbindDevices(
            @ApiParam("探漏分段ID") @PathVariable String id,
            @ApiParam("设备ID列表") @RequestBody List<String> deviceIds) throws ThingsboardException {
        try {
            log.info("解绑探漏分段的设备，分段ID: {}，设备ID列表: {}", id, deviceIds);
            
            // 获取当前分段信息
            LeakDetectionSegmentEntity segment = leakDetectionSegmentService.getSegmentById(id);
            if (segment == null) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body("探漏分段不存在");
            }
            
            // 获取已绑定的设备ID字符串
            String existingDeviceIdsStr = segment.getDeviceIds();
            List<String> existingDeviceIds = new ArrayList<>();
            
            // 如果已有设备ID，将其转换为列表
            if (existingDeviceIdsStr != null && !existingDeviceIdsStr.isEmpty()) {
                existingDeviceIds = new ArrayList<>(Arrays.asList(existingDeviceIdsStr.split(",")));
            } else {
                // 如果没有已绑定的设备，直接返回成功
                Map<String, Object> response = new HashMap<>();
                response.put("success", true);
                response.put("message", "没有需要解绑的设备");
                response.put("deviceIds", "");
                return ResponseEntity.ok(response);
            }
            
            // 移除指定的设备ID
            existingDeviceIds.removeAll(deviceIds);
            
            // 将设备ID列表转换为逗号分隔的字符串
            String newDeviceIdsStr = String.join(",", existingDeviceIds);
            
            // 更新设备绑定关系
            LambdaUpdateWrapper<LeakDetectionSegmentEntity> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(LeakDetectionSegmentEntity::getId, id)
                    .set(LeakDetectionSegmentEntity::getDeviceIds, newDeviceIdsStr);
            boolean result = leakDetectionSegmentService.update(updateWrapper);
            
            if (!result) {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("设备解绑失败");
            }
            
            // 创建响应数据
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "设备解绑成功");
            response.put("deviceIds", newDeviceIdsStr);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Failed to unbind devices from segment, id: {}", id, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(e.getMessage());
        }
    }
    
    @ApiOperation("获取探漏分段绑定的设备")
    @GetMapping("/{id}/devices")
    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN', 'CUSTOMER_USER')")
    public ResponseEntity<?> getSegmentDevices(@ApiParam("探漏分段ID") @PathVariable String id) throws ThingsboardException {
        try {
            // 获取当前分段信息
            LeakDetectionSegmentEntity segment = leakDetectionSegmentService.getSegmentById(id);
            if (segment == null) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body("探漏分段不存在");
            }
            
            // 获取设备ID字符串
            String deviceIdsStr = segment.getDeviceIds();
            List<String> deviceIds = new ArrayList<>();
            
            // 如果有设备ID，将其转换为列表
            if (deviceIdsStr != null && !deviceIdsStr.isEmpty()) {
                deviceIds = Arrays.asList(deviceIdsStr.split(","));
            }
            
            return ResponseEntity.ok(deviceIds);
        } catch (Exception e) {
            log.error("Failed to get segment devices, id: {}", id, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(e.getMessage());
        }
    }

    @ApiOperation("获取可用设备列表（未绑定到指定分段的设备）")
    @GetMapping("/availableDevices")
    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN', 'CUSTOMER_USER')")
    public ResponseEntity<?> getAvailableDevices(
            @ApiParam("分区ID") @RequestParam String partitionId,
            @ApiParam("分段ID") @RequestParam String segmentId,
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer page,
            @ApiParam("每页记录数") @RequestParam(defaultValue = "10") Integer size,
            @ApiParam("设备名称") @RequestParam(required = false) String name,
            @ApiParam("设备类型") @RequestParam(required = false) String type) throws ThingsboardException {
        try {
            log.info("获取可用设备列表，分区ID: {}, 分段ID: {}, 名称: {}, 类型: {}", partitionId, segmentId, name, type);
            
            // 获取当前分段信息，用于排除已绑定的设备
            LeakDetectionSegmentEntity segment = leakDetectionSegmentService.getSegmentById(segmentId);
            List<String> boundDeviceIds = new ArrayList<>();
            
            if (segment != null && segment.getDeviceIds() != null && !segment.getDeviceIds().isEmpty()) {
                boundDeviceIds = Arrays.asList(segment.getDeviceIds().split(","));
            }
            
            // 调用服务获取分区下的可用设备
            Page<DeviceDTO> devicePage = leakDetectionSegmentService.getAvailableDevices(
                partitionId, boundDeviceIds, page, size, name, type);
            
            // 构造返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("list", devicePage.getRecords());
            result.put("total", devicePage.getTotal());
            result.put("pages", devicePage.getPages());
            result.put("pageSize", devicePage.getSize());
            result.put("pageNum", devicePage.getCurrent());
            
            // 包装响应
            Map<String, Object> response = new HashMap<>();
            response.put("code", 200);
            response.put("message", "获取成功");
            response.put("data", result);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Failed to get available devices", e);
            
            // 构造错误响应
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", 500);
            errorResponse.put("message", "获取可用设备列表失败: " + e.getMessage());
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }
    
    @ApiOperation("获取已绑定设备列表")
    @GetMapping("/boundDevices")
    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN', 'CUSTOMER_USER')")
    public ResponseEntity<?> getBoundDevices(
            @ApiParam("分段ID") @RequestParam String segmentId) throws ThingsboardException {
        try {
            log.info("获取已绑定设备列表，分段ID: {}", segmentId);
            
            // 获取当前分段信息
            LeakDetectionSegmentEntity segment = leakDetectionSegmentService.getSegmentById(segmentId);
            if (segment == null) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("code", 404);
                errorResponse.put("message", "探漏分段不存在");
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(errorResponse);
            }
            
            // 获取已绑定设备ID
            List<String> deviceIds = new ArrayList<>();
            if (segment.getDeviceIds() != null && !segment.getDeviceIds().isEmpty()) {
                deviceIds = Arrays.asList(segment.getDeviceIds().split(","));
            }
            
            // 获取方案ID后再获取分区ID
            String planId = segment.getPlanId();
            if (planId == null || planId.isEmpty()) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("code", 500);
                errorResponse.put("message", "获取分区ID失败");
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
            }
            LeakDetectionPlanEntity  plan = leakDetectionPlanService.getPlanById(planId);
            String partitionId = plan.getPartitionId();
            
            // 获取设备详情，传入分区ID
            List<DeviceDTO> boundDevices = leakDetectionSegmentService.getDeviceDetailsByIds(deviceIds, partitionId);
            
            // 包装响应
            Map<String, Object> response = new HashMap<>();
            response.put("code", 200);
            response.put("message", "获取成功");
            response.put("data", boundDevices);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Failed to get bound devices", e);
            
            // 构造错误响应
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", 500);
            errorResponse.put("message", "获取已绑定设备列表失败: " + e.getMessage());
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }
    
    @ApiOperation("绑定设备到探漏分段（新接口）")
    @PostMapping("/bindDevices")
    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN', 'CUSTOMER_USER')")
    public ResponseEntity<?> bindDevices(@RequestBody Map<String, Object> request) throws ThingsboardException {
        try {
            String segmentId = (String) request.get("segmentId");
            @SuppressWarnings("unchecked")
            List<String> deviceIds = (List<String>) request.get("deviceIds");
            
            log.info("绑定设备到探漏分段（新接口），分段ID: {}，设备ID列表: {}", segmentId, deviceIds);
            
            if (segmentId == null || deviceIds == null) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("code", 400);
                errorResponse.put("message", "参数错误：分段ID和设备ID列表不能为空");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse);
            }
            
            // 获取当前分段信息
            LeakDetectionSegmentEntity segment = leakDetectionSegmentService.getSegmentById(segmentId);
            if (segment == null) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("code", 404);
                errorResponse.put("message", "探漏分段不存在");
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(errorResponse);
            }
            
            // 获取已绑定的设备ID字符串
            String existingDeviceIdsStr = segment.getDeviceIds();
            List<String> existingDeviceIds = new ArrayList<>();
            
            // 如果已有设备ID，将其转换为列表
            if (existingDeviceIdsStr != null && !existingDeviceIdsStr.isEmpty()) {
                existingDeviceIds = new ArrayList<>(Arrays.asList(existingDeviceIdsStr.split(",")));
            }
            
            // 合并设备ID列表（去重）
            for (String deviceId : deviceIds) {
                if (!existingDeviceIds.contains(deviceId)) {
                    existingDeviceIds.add(deviceId);
                }
            }
            
            // 更新设备绑定关系
            boolean result = leakDetectionSegmentService.updateSegmentDevices(segmentId, existingDeviceIds);
            
            if (!result) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("code", 500);
                errorResponse.put("message", "设备绑定失败");
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
            }
            
            // 创建响应数据
            Map<String, Object> response = new HashMap<>();
            response.put("code", 200);
            response.put("message", "设备绑定成功");
            response.put("data", String.join(",", existingDeviceIds));
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Failed to bind devices to segment", e);
            
            // 构造错误响应
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", 500);
            errorResponse.put("message", "绑定设备失败: " + e.getMessage());
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }
    
    @ApiOperation("解绑探漏分段的设备（新接口）")
    @PostMapping("/unbindDevices")
    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN', 'CUSTOMER_USER')")
    public ResponseEntity<?> unbindDevices(@RequestBody Map<String, Object> request) throws ThingsboardException {
        try {
            String segmentId = (String) request.get("segmentId");
            @SuppressWarnings("unchecked")
            List<String> deviceIds = (List<String>) request.get("deviceIds");
            
            log.info("解绑探漏分段的设备（新接口），分段ID: {}，设备ID列表: {}", segmentId, deviceIds);
            
            if (segmentId == null || deviceIds == null) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("code", 400);
                errorResponse.put("message", "参数错误：分段ID和设备ID列表不能为空");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResponse);
            }
            
            // 获取当前分段信息
            LeakDetectionSegmentEntity segment = leakDetectionSegmentService.getSegmentById(segmentId);
            if (segment == null) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("code", 404);
                errorResponse.put("message", "探漏分段不存在");
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(errorResponse);
            }
            
            // 获取已绑定的设备ID字符串
            String existingDeviceIdsStr = segment.getDeviceIds();
            List<String> existingDeviceIds = new ArrayList<>();
            
            // 如果已有设备ID，将其转换为列表
            if (existingDeviceIdsStr != null && !existingDeviceIdsStr.isEmpty()) {
                existingDeviceIds = new ArrayList<>(Arrays.asList(existingDeviceIdsStr.split(",")));
            } else {
                // 如果没有已绑定的设备，直接返回成功
                Map<String, Object> response = new HashMap<>();
                response.put("code", 200);
                response.put("message", "没有需要解绑的设备");
                response.put("data", "");
                return ResponseEntity.ok(response);
            }
            
            // 移除指定的设备ID
            existingDeviceIds.removeAll(deviceIds);
            
            // 更新设备绑定关系
            boolean result = leakDetectionSegmentService.updateSegmentDevices(segmentId, existingDeviceIds);
            
            if (!result) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("code", 500);
                errorResponse.put("message", "设备解绑失败");
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
            }
            
            // 创建响应数据
            Map<String, Object> response = new HashMap<>();
            response.put("code", 200);
            response.put("message", "设备解绑成功");
            response.put("data", String.join(",", existingDeviceIds));
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Failed to unbind devices from segment", e);
            
            // 构造错误响应
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", 500);
            errorResponse.put("message", "解绑设备失败: " + e.getMessage());
            
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
        }
    }

    @ApiOperation("获取设备监测数据")
    @GetMapping("/device/monitoring/data")
    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN', 'CUSTOMER_USER')")
    public ResponseEntity<?> getDeviceMonitoringData(
            @ApiParam("设备ID") @RequestParam String deviceId,
            @ApiParam("设备类型") @RequestParam String deviceType,
            @ApiParam("开始日期") @RequestParam(required = false) String startDate,
            @ApiParam("结束日期") @RequestParam(required = false) String endDate) throws ThingsboardException {
        try {
            log.info("获取设备监测数据，设备ID: {}, 设备类型: {}, 开始日期: {}, 结束日期: {}", 
                    deviceId, deviceType, startDate, endDate);
            
            // 生成模拟数据
            List<Map<String, Object>> data = generateMonitoringData(deviceType, startDate, endDate);
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 200);
            result.put("message", "success");
            result.put("data", data);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("Failed to get device monitoring data", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(e.getMessage());
        }
    }
    
    @ApiOperation("获取设备最新上报数据")
    @GetMapping("/device/latest")
    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN', 'CUSTOMER_USER')")
    public ResponseEntity<?> getLatestDeviceData(
            @ApiParam("分段ID") @RequestParam(required = false) String segmentId,
            @ApiParam("设备ID") @RequestParam(required = false) String deviceId,
            @ApiParam("设备类型：1-流量计，2-压力表") @RequestParam(required = false) String deviceType) throws ThingsboardException {
        try {
            log.info("获取设备最新上报数据，分段ID: {}, 设备ID: {}, 设备类型: {}", segmentId, deviceId, deviceType);
            
            // 验证参数
            if (segmentId == null && deviceId == null) {
                Map<String, Object> errorResult = new HashMap<>();
                errorResult.put("code", 400);
                errorResult.put("message", "分段ID和设备ID至少提供一个");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(errorResult);
            }
            
            List<Map<String, Object>> resultList = new ArrayList<>();
            
            // 如果提供了分段ID，获取该分段下的设备的最新数据
            if (segmentId != null) {
                // 获取分段信息
                LeakDetectionSegmentEntity segment = leakDetectionSegmentService.getSegmentById(segmentId);
                if (segment == null) {
                    Map<String, Object> errorResult = new HashMap<>();
                    errorResult.put("code", 404);
                    errorResult.put("message", "分段不存在");
                    return ResponseEntity.status(HttpStatus.NOT_FOUND).body(errorResult);
                }
                
                // 获取分段绑定的设备ID列表
                List<String> deviceIds = new ArrayList<>();
                if (segment.getDeviceIds() != null && !segment.getDeviceIds().isEmpty()) {
                    deviceIds = Arrays.asList(segment.getDeviceIds().split(","));
                }
                
                // 如果没有绑定设备，返回空结果
                if (deviceIds.isEmpty()) {
                    Map<String, Object> result = new HashMap<>();
                    result.put("code", 200);
                    result.put("message", "分段未绑定任何设备");
                    result.put("data", resultList);
                    return ResponseEntity.ok(result);
                }
                
                // 获取方案ID和分区ID
                String planId = segment.getPlanId();
                LeakDetectionPlanEntity plan = leakDetectionPlanService.getPlanById(planId);
                String partitionId = plan.getPartitionId();
                
                // 获取设备详情
                List<DeviceDTO> devices = leakDetectionSegmentService.getDeviceDetailsByIds(deviceIds, partitionId);
                
                // 为每个设备生成最新数据
                for (DeviceDTO device : devices) {
                    // 如果指定了设备类型且不匹配，则跳过
                    if (deviceType != null && !deviceType.equals(device.getType())) {
                        continue;
                    }
                    
                    // 生成该设备的最新数据
                    Map<String, Object> deviceData = generateLatestDeviceData(
                            device.getDeviceId(), device.getType(), device.getName());
                    resultList.add(deviceData);
                }
            }
            // 如果提供了设备ID，获取该设备的最新数据
            else if (deviceId != null) {
                // 使用提供的设备类型，如果未提供则默认为流量计
                String actualDeviceType = deviceType != null ? deviceType : "1";
                
                // 模拟设备名称
                String deviceName = "1".equals(actualDeviceType) ? "流量计_" + deviceId : "压力表_" + deviceId;
                
                // 生成设备的最新数据
                Map<String, Object> deviceData = generateLatestDeviceData(deviceId, actualDeviceType, deviceName);
                resultList.add(deviceData);
            }
            
            Map<String, Object> result = new HashMap<>();
            result.put("code", 200);
            result.put("message", "success");
            result.put("data", resultList);
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取设备最新上报数据失败", e);
            Map<String, Object> errorResult = new HashMap<>();
            errorResult.put("code", 500);
            errorResult.put("message", "获取设备最新上报数据失败: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResult);
        }
    }

    /**
     * 生成模拟监测数据
     * @param deviceType 设备类型：1-流量计，2-压力表
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 监测数据列表
     */
    private List<Map<String, Object>> generateMonitoringData(String deviceType, String startDate, String endDate) {
        List<Map<String, Object>> data = new ArrayList<>();
        
        // 解析日期
        LocalDate start = startDate != null ? LocalDate.parse(startDate) : LocalDate.now().minusDays(6);
        LocalDate end = endDate != null ? LocalDate.parse(endDate) : LocalDate.now();
        
        // 生成每天的数据
        for (LocalDate date = start; !date.isAfter(end); date = date.plusDays(1)) {
            // 每天生成3个时间点的数据：早8点、下午2点、晚8点
            int[] hours = {8, 14, 20};
            
            for (int hour : hours) {
                LocalDateTime dateTime = date.atTime(hour, 0);
                
                Map<String, Object> item = new HashMap<>();
                item.put("time", dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                item.put("timestamp", dateTime.toInstant(ZoneOffset.of("+8")).toEpochMilli());
                
                // 根据设备类型生成不同的数据
                if ("1".equals(deviceType)) { // 流量计
                    double flowRate = 100 + Math.random() * 50; // 100-150之间的随机值
                    double leakRate = Math.round((50 + Math.random() * 30)) / 10.0; // 5.0-8.0之间的随机值
                    
                    item.put("flowRate", Math.round(flowRate * 10) / 10.0);
                    item.put("pressure", null);
                    item.put("leakRate", leakRate);
                } else { // 压力表
                    double pressure = Math.round((20 + Math.random() * 10)) / 10.0; // 2.0-3.0之间的随机值
                    double leakRate = Math.round((50 + Math.random() * 30)) / 10.0; // 5.0-8.0之间的随机值
                    
                    item.put("flowRate", null);
                    item.put("pressure", pressure);
                    item.put("leakRate", leakRate);
                }
                
                data.add(item);
            }
        }
        
        // 按时间排序
        data.sort((a, b) -> Long.compare((Long)a.get("timestamp"), (Long)b.get("timestamp")));
        
        return data;
    }
    
    /**
     * 生成设备的最新上报数据
     * @param deviceId 设备ID
     * @param deviceType 设备类型：1-流量计，2-压力表
     * @param deviceName 设备名称
     * @return 最新上报数据
     */
    private Map<String, Object> generateLatestDeviceData(String deviceId, String deviceType, String deviceName) {
        Map<String, Object> data = new HashMap<>();
        
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();
        String timeStr = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        long timestamp = now.toInstant(ZoneOffset.of("+8")).toEpochMilli();
        
        // 设备基础信息
        data.put("deviceId", deviceId);
        data.put("deviceName", deviceName);
        data.put("deviceType", deviceType);
        data.put("deviceTypeName", "1".equals(deviceType) ? "流量计" : "压力表");
        data.put("reportTime", timeStr);
        data.put("timestamp", timestamp);
        
        // 根据设备类型生成不同的监测数据
        if ("1".equals(deviceType)) { // 流量计
            double flowRate = 100 + Math.random() * 50; // 100-150之间的随机值
            double leakRate = Math.round((50 + Math.random() * 30)) / 10.0; // 5.0-8.0之间的随机值
            
            data.put("flowRate", Math.round(flowRate * 10) / 10.0);
            data.put("pressure", null);
            data.put("leakRate", leakRate);
            data.put("status", Math.random() > 0.1 ? "normal" : "alarm"); // 90%概率正常状态
        } else { // 压力表
            double pressure = Math.round((20 + Math.random() * 10)) / 10.0; // 2.0-3.0之间的随机值
            double leakRate = Math.round((50 + Math.random() * 30)) / 10.0; // 5.0-8.0之间的随机值
            
            data.put("flowRate", null);
            data.put("pressure", pressure);
            data.put("leakRate", leakRate);
            data.put("status", Math.random() > 0.1 ? "normal" : "alarm"); // 90%概率正常状态
        }
        
        return data;
    }
} 