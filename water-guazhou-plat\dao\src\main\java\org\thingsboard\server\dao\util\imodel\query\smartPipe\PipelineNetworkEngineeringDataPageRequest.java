package org.thingsboard.server.dao.util.imodel.query.smartPipe;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.thingsboard.server.dao.model.sql.base.BaseDatabaseConnection;
import org.thingsboard.server.dao.model.sql.smartPipe.PipelineNetworkEngineeringData;
import org.thingsboard.server.dao.util.imodel.query.PageableQueryEntity;

import java.util.Date;

/**
 * 管网采集-工程数据对象 pipeline_network_engineering_data
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@ApiModel(value = "工程数据对象", description = "管网采集-工程数据对象实体类")
@Data
public class PipelineNetworkEngineeringDataPageRequest extends PageableQueryEntity<PipelineNetworkEngineeringData> {

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 工程全称
     */
    @ApiModelProperty(value = "工程全称")
    private String name;

    /**
     * 工程编号
     */
    @ApiModelProperty(value = "工程编号")
    private String code;

    /**
     * 工程简称
     */
    @ApiModelProperty(value = "工程简称")
    private String nameAbb;

    /**
     * 工程性质
     */
    @ApiModelProperty(value = "工程性质")
    private String properties;

    /**
     * 所属区划
     */
    @ApiModelProperty(value = "所属区划")
    private String area;

    /**
     * 测量单位
     */
    @ApiModelProperty(value = "测量单位")
    private String measurementUnit;

    /**
     * 指派员工
     */
    @ApiModelProperty(value = "指派员工")
    private String assignEmployees;

    /**
     * 工程范围
     */
    @ApiModelProperty(value = "工程范围")
    private String worksScope;

    /**
     * 开始日期
     */
    @ApiModelProperty(value = "开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    /**
     * 结束日期
     */
    @ApiModelProperty(value = "结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    /**
     * 租户id
     */
    @ApiModelProperty(value = "租户id")
    private String tenantId;

    /**
     * 提交状态(0-未提交 1-已提交)
     */
    @ApiModelProperty(value = "提交状态(0-未提交 1-已提交)")
    private String isCommit;

    /**
     * 审核状态(0-未审核 1-已审核 2-已退回)
     */
    @ApiModelProperty(value = "审核状态(0-未审核 1-已审核 2-已退回)")
    private String isAudit;

    /**
     * 入库状态(0-待入库 1-已入库)
     */
    @ApiModelProperty(value = "入库状态(0-待入库 1-已入库)")
    private String isStorage;

    /**
     * 开始日期
     */
    @ApiModelProperty(value = "开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 结束日期
     */
    @ApiModelProperty(value = "结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
