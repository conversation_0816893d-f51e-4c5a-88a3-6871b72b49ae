package org.thingsboard.server.dao.model.sql.remoteControl;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 设备控制操作日志实体类
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@Data
@TableName("device_control_log_setting")
public class DeviceControlLogSetting {

    /**
     * 主键ID
     */
    @TableId
    private String id;

    /**
     * 控制记录ID
     */
    private String recordId;

    /**
     * 操作类型(create/update/delete/execute)
     */
    private String operationType;

    /**
     * 操作内容(JSON格式)
     */
    private String operationContent;

    /**
     * 操作员ID
     */
    private String operatorId;

    /**
     * 操作员姓名
     */
    private String operatorName;

    /**
     * 操作时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date operationTime;

    /**
     * IP地址
     */
    private String ipAddress;

    /**
     * 用户代理
     */
    private String userAgent;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}
