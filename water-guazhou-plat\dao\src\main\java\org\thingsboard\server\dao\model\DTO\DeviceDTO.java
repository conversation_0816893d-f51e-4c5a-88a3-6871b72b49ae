package org.thingsboard.server.dao.model.DTO;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import lombok.Builder;

import java.util.Date;

/**
 * 设备数据传输对象
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DeviceDTO {

    /**
     * 设备ID
     */
    private String deviceId;
    
    /**
     * 设备名称
     */
    private String name;
    
    /**
     * 设备类型
     * 1: 流量计
     * 2: 压力计
     * 3: 大用户
     */
    private String type;
    
    /**
     * 设备标签
     */
    private String label;
    
    /**
     * 附加信息
     */
    private String additionalInfo;
    
    /**
     * 分区ID
     */
    private String partitionId;
    
    /**
     * 水流方向
     * 1: 正进负出
     * 2: 负进正出
     * 3: 入口表
     * 4: 出口表
     */
    private String direction;
    
    /**
     * 是否核算
     * 0: 否
     * 1: 是
     */
    private String isAccount;
    
    /**
     * 创建时间
     */
    private Date createTime;
} 