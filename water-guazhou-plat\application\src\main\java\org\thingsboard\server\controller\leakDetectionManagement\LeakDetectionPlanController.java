package org.thingsboard.server.controller.leakDetectionManagement;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.thingsboard.server.common.data.exception.ThingsboardException;
import org.thingsboard.server.controller.base.BaseController;
import org.thingsboard.server.dao.leakDetectionPlan.LeakDetectionPlanService;
import org.thingsboard.server.dao.model.DTO.LeakDetectionPlanDTO;
import org.thingsboard.server.dao.model.sql.leakDetectionPlan.LeakDetectionPlanEntity;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/leakDetection/plan")
@Api(tags = "探漏方案管理接口")
@Slf4j
public class LeakDetectionPlanController extends BaseController {

    @Autowired
    private LeakDetectionPlanService leakDetectionPlanService;

    @ApiOperation("保存探漏方案")
    @PostMapping
    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN', 'CUSTOMER_USER')")
    public ResponseEntity<?> savePlan(@RequestBody LeakDetectionPlanEntity plan) throws ThingsboardException {
        try {
            log.info("保存探漏方案，方案数据: {}", plan);
            // 记录分区ID
            if (plan.getPartitionId() != null) {
                log.info("方案关联的分区ID: {}", plan.getPartitionId());
            }
            
            plan = leakDetectionPlanService.saveOrUpdatePlan(plan);
            return ResponseEntity.ok(plan);
        } catch (Exception e) {
            log.error("Failed to save leak detection plan", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(e.getMessage());
        }
    }

    @ApiOperation("保存探漏方案及其分段数据")
    @PostMapping("/withSegments")
    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN', 'CUSTOMER_USER')")
    public ResponseEntity<?> savePlanWithSegments(@RequestBody LeakDetectionPlanDTO planDTO) throws ThingsboardException {
        try {
            log.info("保存探漏方案及其分段数据，方案数据: {}", planDTO.getPlan());
            // 记录分区ID
            if (planDTO.getPlan() != null && planDTO.getPlan().getPartitionId() != null) {
                log.info("方案关联的分区ID: {}", planDTO.getPlan().getPartitionId());
            }
            
            planDTO = leakDetectionPlanService.saveOrUpdatePlanWithSegments(planDTO);
            return ResponseEntity.ok(planDTO);
        } catch (Exception e) {
            log.error("Failed to save leak detection plan with segments", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(e.getMessage());
        }
    }

    @ApiOperation("获取探漏方案详情")
    @GetMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN', 'CUSTOMER_USER')")
    public ResponseEntity<?> getPlanById(@ApiParam("探漏方案ID") @PathVariable String id) throws ThingsboardException {
        try {
            LeakDetectionPlanEntity plan = leakDetectionPlanService.getPlanById(id);
            if (plan == null) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body("探漏方案不存在");
            }
            return ResponseEntity.ok(plan);
        } catch (Exception e) {
            log.error("Failed to get leak detection plan by id: {}", id, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(e.getMessage());
        }
    }

    @ApiOperation("获取探漏方案及其分段数据详情")
    @GetMapping("/withSegments/{id}")
    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN', 'CUSTOMER_USER')")
    public ResponseEntity<?> getPlanWithSegmentsById(@ApiParam("探漏方案ID") @PathVariable String id) throws ThingsboardException {
        try {
            LeakDetectionPlanDTO planDTO = leakDetectionPlanService.getPlanWithSegmentsById(id);
            if (planDTO == null || planDTO.getPlan() == null) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body("探漏方案不存在");
            }
            return ResponseEntity.ok(planDTO);
        } catch (Exception e) {
            log.error("Failed to get leak detection plan with segments by id: {}", id, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(e.getMessage());
        }
    }

    @ApiOperation("分页查询探漏方案")
    @GetMapping
    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN', 'CUSTOMER_USER')")
    public ResponseEntity<?> getPlans(
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam("每页记录数") @RequestParam(defaultValue = "10") Integer pageSize,
            @ApiParam("方案名称") @RequestParam(required = false) String name,
            @ApiParam("区域编码") @RequestParam(required = false) String areaCode,
            @ApiParam("分区ID") @RequestParam(required = false) String partitionId,
            @ApiParam("状态") @RequestParam(required = false) String status,
            @ApiParam("开始时间") @RequestParam(required = false) String startTime,
            @ApiParam("结束时间") @RequestParam(required = false) String endTime) throws ThingsboardException {
        try {
            Page<LeakDetectionPlanEntity> page = 
                    new Page<>(pageNum, pageSize);
            
            // 添加分区ID筛选
            page = leakDetectionPlanService.queryPlansByPage(page, name, areaCode, partitionId, status, startTime, endTime);
            
            Map<String, Object> result = new HashMap<>();
            result.put("total", page.getTotal());
            result.put("pageSize", page.getSize());
            result.put("pageNum", page.getCurrent());
            result.put("pages", page.getPages());
            result.put("list", page.getRecords());
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("Failed to query leak detection plans by page", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(e.getMessage());
        }
    }

    @ApiOperation("删除探漏方案")
    @DeleteMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN', 'CUSTOMER_USER')")
    public ResponseEntity<?> deletePlan(@ApiParam("探漏方案ID") @PathVariable String id) throws ThingsboardException {
        try {
            boolean success = leakDetectionPlanService.deletePlan(id);
            if (!success) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body("探漏方案不存在或删除失败");
            }
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            log.error("Failed to delete leak detection plan by id: {}", id, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(e.getMessage());
        }
    }

    @ApiOperation("删除探漏方案及其分段数据")
    @DeleteMapping("/withSegments/{id}")
    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN', 'CUSTOMER_USER')")
    public ResponseEntity<?> deletePlanWithSegments(@ApiParam("探漏方案ID") @PathVariable String id) throws ThingsboardException {
        try {
            boolean success = leakDetectionPlanService.deletePlanWithSegments(id);
            if (!success) {
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body("探漏方案不存在或删除失败");
            }
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            log.error("Failed to delete leak detection plan with segments by id: {}", id, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(e.getMessage());
        }
    }
    
    @ApiOperation("根据分区ID查询探漏方案")
    @GetMapping("/byPartition/{partitionId}")
    @PreAuthorize("hasAnyAuthority('TENANT_ADMIN', 'CUSTOMER_USER')")
    public ResponseEntity<?> getPlansByPartitionId(
            @ApiParam("分区ID") @PathVariable String partitionId,
            @ApiParam("页码") @RequestParam(defaultValue = "1") Integer pageNum,
            @ApiParam("每页记录数") @RequestParam(defaultValue = "10") Integer pageSize) throws ThingsboardException {
        try {
            log.info("根据分区ID查询探漏方案，分区ID: {}", partitionId);
            
            Page<LeakDetectionPlanEntity> page = new Page<>(pageNum, pageSize);
            page = leakDetectionPlanService.queryPlansByPage(page, null, null, partitionId, null, null, null);
            
            Map<String, Object> result = new HashMap<>();
            result.put("total", page.getTotal());
            result.put("pageSize", page.getSize());
            result.put("pageNum", page.getCurrent());
            result.put("pages", page.getPages());
            result.put("list", page.getRecords());
            
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("Failed to query leak detection plans by partition id: {}", partitionId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(e.getMessage());
        }
    }
}