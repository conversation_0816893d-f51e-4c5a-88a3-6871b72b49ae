package org.thingsboard.server.dao.orderWork;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.thingsboard.server.dao.model.DTO.WorkOrderVisitMsgDTO;
import org.thingsboard.server.dao.model.sql.statistic.GeneralTaskStatusStatistic;
import org.thingsboard.server.dao.model.sql.workOrder.*;
import org.thingsboard.server.dao.util.imodel.query.smartService.SendVisitMsgRequest;
import org.thingsboard.server.dao.util.imodel.query.smartService.WorkOrderListMsgRequest;
import org.thingsboard.server.dao.util.imodel.query.workOrder.*;

import java.util.List;
import java.util.Set;

public interface NewlyWorkOrderService {
    /**
     * 分页条件查询工单
     *
     * @param request 分页查询条件
     * @return 分页查询结果
     */
    IPage<WorkOrder> findWorkOrderByPage(WorkOrderPageRequest request);

    /**
     * 保存工单
     *
     * @param workOrder 实体信息
     * @return 保存好的实体
     */
    WorkOrder save(WorkOrderSaveRequest workOrder);

    /**
     * 工单是否为指定状态
     *
     * @param orderId 唯一标识
     * @param status  目标状态
     * @return 是否为指定状态
     */
    boolean isStatus(String orderId, WorkOrderStatus status);

    /**
     * 获取工单状态
     *
     * @param orderId 唯一标识
     * @return 工单状态
     */
    WorkOrderStatus getStatus(String orderId);

    /**
     * 获取工单当前处理人的id
     *
     * @param orderId 唯一标识
     * @return 工单当前处理人的id
     */
    String getStepProcessUserId(String orderId);

    /**
     * 获取上n次的处理人的id
     *
     * @param orderId 唯一标识
     * @param order   第n
     * @return 上n次处理人的id
     */
    String getPrevDetailProcessUserId(String orderId, int order);

    /**
     * 获取上n次的下一步处理人的id
     *
     * @param orderId 唯一标识
     * @param order   第n
     * @return 上n次处理人的id
     */
    String getPrevDetailNextProcessUserId(String orderId, int order);

    /**
     * 添加工单处理流程，不强制下一步处理人为当前处理人，而是前端传入的处理人
     *
     * @param id      唯一标识
     * @param request 要添加步骤的详细信息
     */
    void addStage(String id, WorkOrderStageRequest request);

    /**
     * 添加工单处理流程，可强制下一步处理人为当前步骤的处理人
     *
     * @param id                     工单ID
     * @param request                要添加步骤的详细信息
     * @param forceOriginalProcessor 目前用于接受工单 强制下一步处理人为当前处理人
     */
    void addStage(String id, WorkOrderStageRequest request, boolean forceOriginalProcessor);

    /**
     * 通过id查询工单
     *
     * @param id 工单id
     * @return 工单
     */
    WorkOrder find(String id);

    /**
     * 通过id查询工单当前的所有步骤
     *
     * @param id 工单id
     * @return 工单当前的所有步骤
     */
    List<WorkOrderDetail> findStages(String id);

    /**
     * 分派工单
     *
     * @param id      工单id
     * @param request 详细信息
     */
    void assign(String id, WorkOrderAssignRequest request);

    /**
     * 工单是否已被分派
     *
     * @param id 工单id
     * @return 是否已被分派
     */
    boolean isAssigned(String id);

    /**
     * 重新指定处理人，在退单、处理中和审核中的时候不可以重新指派
     *
     * @param id      工单id
     * @param request 详细信息
     */
    void reassign(String id, WorkOrderReassignRequest request);

    /**
     * 按类型、处理人来统计工单（分时分类、分类分时统计）
     *
     * @param request 选项
     * @return 统计信息
     */
    WorkOrderStatisticEntity countWorkOrder(WorkOrderCountRequest request);

    /**
     * 申请工单协作
     *
     * @param id       需要协作的工单id
     * @param request  协作详细信息
     * @param tenantId 客户id
     */
    void collaborate(String id, WorkOrderCollaborateRequest request, String tenantId);

    /**
     * 校验工单协作
     *
     * @param id       工单id
     * @param request  审核明细
     * @param tenantId 客户id
     */
    void collaborateVerify(String id, WorkOrderCollaborateVerifyRequest request, String tenantId);

    /**
     * 分页条件查询工单协作申请
     *
     * @param request 明细
     * @return 工单协作申请
     */
    IPage<WorkOrderCollaboration> collaborations(WorkOrderCollaborationPageRequest request);

    /**
     * 获取子工单（协作工单）
     *
     * @param id 主工单id
     * @return 子工单实体
     */
    List<WorkOrder> children(String id);

    /**
     * 工单协作是否为指定状态
     *
     * @param id     工单id
     * @param status 目标状态
     * @return 是否为指定状态
     */
    boolean isStatus(String id, WorkOrderCollaborationStatus status);

    /**
     * 工单处理人是否为指定用户
     *
     * @param id              工单id
     * @param currentUserUUID 指定用户id
     * @return 工单处理人是否为指定用户
     */
    boolean isUserProcess(String id, String currentUserUUID);

    /**
     * 工单协作申请的审核人是否为指定用户
     *
     * @param id              工单协作申请id
     * @param currentUserUUID 审核人id
     * @return 工单协作申请的审核人是否为指定用户
     */
    boolean collaborIsUserProcess(String id, String currentUserUUID);

    Object faultCount(int page, int size, String level, String deviceType, Long startTime, Long endTime, String tenantId);

    Object workTimeCount(int page, int size, String source, Long startTime, Long endTime, String tenantId);

    /**
     * 统计工单完成情况
     *
     * @param request 过滤条件
     * @return 完成情况
     */
    WorkOrderCompleteCountResponse countWorkOrderComplete(WorkOrderCompleteCountRequest request);

    /**
     * 每阶段数量统计
     *
     * @param req 过滤条件
     * @return 统计信息
     */
    WorkOrderCountOfStatusResponse countOfStatus(MutableWorkOrderCountOfStatusRequest req);

    /**
     * 退单审核
     *
     * @param id      工单id
     * @param request 明细
     * @return 是否成功
     */
    boolean chargeback(String id, WorkOrderStageRequest request);

    /**
     * 退单申请
     *
     * @param id      工单id
     * @param request 明细
     * @return 是否成功
     */
    boolean chargebackRequest(String id, WorkOrderStageRequest request);

    /**
     * 统计指定用户的指定状态工单
     *
     * @param userId 用户id
     * @param status 指定状态
     * @return 指定用户的指定状态工单统计信息
     */
    GeneralTaskStatusStatistic countStatusByUser(String userId, Set<WorkOrderStatus> status);

    /**
     * 是否允许提交审核
     * 1. 协作工单已完成
     *
     * @param id 唯一标识
     * @return 是否允许提交审核
     */
    boolean canSubmit(String id);

    IPage<WorkOrderVisitMsgDTO> getVisitList(WorkOrderListMsgRequest request);

    List<WorkOrderVisitMsgDTO> getWorkOrderMsgList(SendVisitMsgRequest sendVisitMsgRequest);

    void updateById(WorkOrder workOrder);

}
