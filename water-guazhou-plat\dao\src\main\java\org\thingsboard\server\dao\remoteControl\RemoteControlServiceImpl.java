package org.thingsboard.server.dao.remoteControl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.thingsboard.server.dao.model.sql.remoteControl.DeviceControlRecordSetting;
import org.thingsboard.server.dao.model.sql.remoteControl.RemoteControlDeviceSetting;

import java.util.*;

/**
 * 远程设备控制服务实现类
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@Slf4j
@Service
public class RemoteControlServiceImpl implements RemoteControlService {

    @Autowired
    private DeviceControlRecordMapper deviceControlRecordMapper;

    @Autowired
    private RemoteControlDeviceMapper remoteControlDeviceMapper;

    // ==================== 设备控制记录相关 ====================

    @Override
    public IPage<DeviceControlRecordSetting> getControlRecordList(
            int page, int size, String deviceId, String deviceName,
            String controlType, String status, String operatorName, String tenantId) {
        
        Page<DeviceControlRecordSetting> pageParam = new Page<>(page, size);
        return deviceControlRecordMapper.selectPageList(
                pageParam, deviceId, deviceName, controlType, status, operatorName, tenantId
        );
    }

    @Override
    public List<DeviceControlRecordSetting> getControlRecordsByDeviceId(String deviceId, String tenantId) {
        return deviceControlRecordMapper.selectByDeviceId(deviceId, tenantId);
    }

    @Override
    @Transactional
    public DeviceControlRecordSetting saveControlRecord(DeviceControlRecordSetting record) {
        if (StringUtils.isEmpty(record.getId())) {
            record.setId(UUID.randomUUID().toString().replace("-", ""));
            record.setCreateTime(new Date());
        }
        record.setUpdateTime(new Date());
        
        deviceControlRecordMapper.insert(record);
        log.info("保存设备控制记录成功，记录ID: {}, 设备ID: {}", record.getId(), record.getDeviceId());
        return record;
    }

    @Override
    @Transactional
    public List<DeviceControlRecordSetting> batchSaveControlRecords(List<DeviceControlRecordSetting> records) {
        List<DeviceControlRecordSetting> savedRecords = new ArrayList<>();
        for (DeviceControlRecordSetting record : records) {
            savedRecords.add(saveControlRecord(record));
        }
        return savedRecords;
    }

    @Override
    @Transactional
    public boolean deleteControlRecord(String recordId, String tenantId) {
        QueryWrapper<DeviceControlRecordSetting> wrapper = new QueryWrapper<>();
        wrapper.eq("id", recordId).eq("tenant_id", tenantId);
        
        int result = deviceControlRecordMapper.delete(wrapper);
        log.info("删除设备控制记录，记录ID: {}, 结果: {}", recordId, result > 0);
        return result > 0;
    }

    @Override
    @Transactional
    public boolean updateControlRecordStatus(String recordId, String status, String tenantId) {
        DeviceControlRecordSetting record = new DeviceControlRecordSetting();
        record.setId(recordId);
        record.setStatus(status);
        record.setUpdateTime(new Date());
        
        QueryWrapper<DeviceControlRecordSetting> wrapper = new QueryWrapper<>();
        wrapper.eq("id", recordId).eq("tenant_id", tenantId);
        
        int result = deviceControlRecordMapper.update(record, wrapper);
        log.info("更新设备控制记录状态，记录ID: {}, 新状态: {}, 结果: {}", recordId, status, result > 0);
        return result > 0;
    }

    // ==================== 远程控制设备相关 ====================

    @Override
    public IPage<RemoteControlDeviceSetting> getRemoteControlDeviceList(
            int page, int size, String waterPlantId, String deviceType, String location, String tenantId) {
        
        Page<RemoteControlDeviceSetting> pageParam = new Page<>(page, size);
        return remoteControlDeviceMapper.selectPageList(pageParam, waterPlantId, deviceType, location, tenantId);
    }

    @Override
    public List<RemoteControlDeviceSetting> getDevicesByWaterPlantId(String waterPlantId, String tenantId) {
        return remoteControlDeviceMapper.selectByWaterPlantId(waterPlantId, tenantId);
    }

    @Override
    public RemoteControlDeviceSetting getRemoteControlDeviceById(String deviceId, String tenantId) {
        return remoteControlDeviceMapper.selectByDeviceId(deviceId, tenantId);
    }

    @Override
    @Transactional
    public RemoteControlDeviceSetting saveRemoteControlDevice(RemoteControlDeviceSetting device) {
        if (StringUtils.isEmpty(device.getId())) {
            device.setId(UUID.randomUUID().toString().replace("-", ""));
            device.setCreateTime(new Date());
        }
        device.setUpdateTime(new Date());
        
        remoteControlDeviceMapper.insert(device);
        log.info("保存远程控制设备信息成功，设备ID: {}", device.getDeviceId());
        return device;
    }

    @Override
    @Transactional
    public boolean deleteRemoteControlDevice(String deviceId, String tenantId) {
        QueryWrapper<RemoteControlDeviceSetting> wrapper = new QueryWrapper<>();
        wrapper.eq("device_id", deviceId).eq("tenant_id", tenantId);
        
        int result = remoteControlDeviceMapper.delete(wrapper);
        log.info("删除远程控制设备信息，设备ID: {}, 结果: {}", deviceId, result > 0);
        return result > 0;
    }

    // ==================== 统计相关 ====================

    @Override
    public List<Map<String, Object>> getDeviceTypeStats(String tenantId) {
        return remoteControlDeviceMapper.countByDeviceType(tenantId);
    }

    @Override
    public Map<String, Object> getControlRecordStats(String tenantId) {
        Map<String, Object> stats = new HashMap<>();
        
        // 统计总记录数
        Long totalCount = deviceControlRecordMapper.countByCondition(null, null, tenantId);
        stats.put("totalCount", totalCount);
        
        // 统计各状态记录数
        Long pendingCount = deviceControlRecordMapper.countByCondition(null, "pending", tenantId);
        Long successCount = deviceControlRecordMapper.countByCondition(null, "success", tenantId);
        Long failedCount = deviceControlRecordMapper.countByCondition(null, "failed", tenantId);
        Long cancelledCount = deviceControlRecordMapper.countByCondition(null, "cancelled", tenantId);
        
        stats.put("pendingCount", pendingCount);
        stats.put("successCount", successCount);
        stats.put("failedCount", failedCount);
        stats.put("cancelledCount", cancelledCount);
        
        return stats;
    }
}
