package org.thingsboard.server.dao.smartPipe.impl;

import java.util.List;
import java.util.UUID;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.model.sql.smartPipe.PipelineNetworkEngineeringData;
import org.thingsboard.server.dao.smartPipe.IPipelineNetworkEngineeringDataService;
import org.thingsboard.server.dao.sql.smartPipe.PipelineNetworkEngineeringDataMapper;
import org.thingsboard.server.dao.util.imodel.query.smartPipe.PipelineNetworkEngineeringDataPageRequest;

/**
 * 管网采集-工程数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-24
 */
@Service
public class PipelineNetworkEngineeringDataServiceImpl implements IPipelineNetworkEngineeringDataService {

    @Autowired
    private PipelineNetworkEngineeringDataMapper pipelineNetworkEngineeringDataMapper;

    /**
     * 查询管网采集-工程数据
     *
     * @param id 管网采集-工程数据主键
     * @return 管网采集-工程数据
     */
    @Override
    public PipelineNetworkEngineeringData selectPipelineNetworkEngineeringDataById(String id) {
        return pipelineNetworkEngineeringDataMapper.selectPipelineNetworkEngineeringDataById(id);
    }

    /**
     * 查询管网采集-工程数据列表
     *
     * @param pipelineNetworkEngineeringData 管网采集-工程数据
     * @return 管网采集-工程数据
     */
    @Override
    public IPage<PipelineNetworkEngineeringData> selectPipelineNetworkEngineeringDataList(PipelineNetworkEngineeringDataPageRequest pipelineNetworkEngineeringData) {
        return pipelineNetworkEngineeringDataMapper.selectPipelineNetworkEngineeringDataList(pipelineNetworkEngineeringData);
    }

    /**
     * 新增管网采集-工程数据
     *
     * @param pipelineNetworkEngineeringData 管网采集-工程数据
     * @return 结果
     */
    @Override
    public int insertPipelineNetworkEngineeringData(PipelineNetworkEngineeringData pipelineNetworkEngineeringData) {
        pipelineNetworkEngineeringData.setId(UUID.randomUUID().toString().replace("-", ""));
        return pipelineNetworkEngineeringDataMapper.insertPipelineNetworkEngineeringData(pipelineNetworkEngineeringData);
    }

    /**
     * 修改管网采集-工程数据
     *
     * @param pipelineNetworkEngineeringData 管网采集-工程数据
     * @return 结果
     */
    @Override
    public int updatePipelineNetworkEngineeringData(PipelineNetworkEngineeringData pipelineNetworkEngineeringData) {
        return pipelineNetworkEngineeringDataMapper.updatePipelineNetworkEngineeringData(pipelineNetworkEngineeringData);
    }

    /**
     * 批量删除管网采集-工程数据
     *
     * @param ids 需要删除的管网采集-工程数据主键
     * @return 结果
     */
    @Override
    public int deletePipelineNetworkEngineeringDataByIds(List<String> ids) {
        return pipelineNetworkEngineeringDataMapper.deletePipelineNetworkEngineeringDataByIds(ids);
    }

    /**
     * 删除管网采集-工程数据信息
     *
     * @param id 管网采集-工程数据主键
     * @return 结果
     */
    @Override
    public int deletePipelineNetworkEngineeringDataById(String id) {
        return pipelineNetworkEngineeringDataMapper.deletePipelineNetworkEngineeringDataById(id);
    }
}
