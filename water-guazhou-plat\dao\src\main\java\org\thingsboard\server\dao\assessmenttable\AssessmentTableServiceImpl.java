package org.thingsboard.server.dao.assessmenttable;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.common.data.page.PageData;
import org.thingsboard.server.dao.model.sql.assessmenttable.AssessmentTableEntity;
import org.thingsboard.server.dao.model.sql.assessmenttable.AssessmentTableItemEntity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 考核表Service实现类
 */
@Slf4j
@Service
@Transactional
public class AssessmentTableServiceImpl implements AssessmentTableService {

    @Autowired
    private AssessmentTableMapper assessmentTableMapper;

    @Autowired
    private AssessmentTableItemMapper assessmentTableItemMapper;

    @Override
    public AssessmentTableDto saveAssessmentTable(AssessmentTableDto assessmentTableDto) {
        log.debug("保存考核表: {}", assessmentTableDto);
        
        // 创建或更新考核表
        AssessmentTableEntity entity = new AssessmentTableEntity(assessmentTableDto);
        
        // 设置创建时间
        if (entity.getCreateTime() == null) {
            entity.setCreateTime(new Date());
        }
        
        // 设置搜索文本
        entity.setSearchText(entity.getSearchTextSource());
        
        // 保存考核表
        if (entity.getId() == null) {
            // 生成UUID字符串作为ID
            String randomId = UUID.randomUUID().toString();
            entity.setId(randomId);
            assessmentTableMapper.insert(entity);
        } else {
            assessmentTableMapper.updateById(entity);
        }
        
        // 处理明细项
        if (assessmentTableDto.getItems() != null && !assessmentTableDto.getItems().isEmpty()) {
            // 先删除原有明细项
            assessmentTableItemMapper.deleteByAssessmentTableId(entity.getId());
            
            // 保存新的明细项
            List<AssessmentTableItemEntity> itemEntities = new ArrayList<>();
            for (AssessmentTableItemDto itemDto : assessmentTableDto.getItems()) {
                AssessmentTableItemEntity itemEntity = new AssessmentTableItemEntity(itemDto);
                // 生成UUID字符串作为ID
                itemEntity.setId(UUID.randomUUID().toString());
                itemEntity.setAssessmentTableId(entity.getId());
                if (itemEntity.getCreateTime() == null) {
                    itemEntity.setCreateTime(new Date());
                }
                itemEntities.add(itemEntity);
            }
            
            // 批量插入明细项
            for (AssessmentTableItemEntity itemEntity : itemEntities) {
                assessmentTableItemMapper.insert(itemEntity);
            }
        }
        
        // 返回保存后的考核表DTO
        return getAssessmentTableById(assessmentTableDto.getTenantId(), UUID.fromString(entity.getId()));
    }

    @Override
    public AssessmentTableDto getAssessmentTableById(TenantId tenantId, UUID id) {
        log.debug("根据ID查询考核表: tenantId={}, id={}", tenantId, id);
        
        // 查询考核表
        AssessmentTableEntity entity = assessmentTableMapper.selectById(id.toString());
        if (entity == null) {
            return null;
        }
        
        // 转换为DTO
        AssessmentTableDto dto = entity.toData();
        
        // 查询明细项
        List<AssessmentTableItemEntity> itemEntities = assessmentTableItemMapper.selectByAssessmentTableId(entity.getId());
        if (itemEntities != null && !itemEntities.isEmpty()) {
            List<AssessmentTableItemDto> itemDtos = itemEntities.stream()
                    .map(AssessmentTableItemEntity::toData)
                    .collect(Collectors.toList());
            dto.setItems(itemDtos);
        }
        
        return dto;
    }

    @Override
    public PageData<AssessmentTableDto> getAssessmentTables(TenantId tenantId, int page, int pageSize, 
                                                           String searchText, String name,
                                                           String region,
                                                           String assessmentType, 
                                                           String status, String period, String partition) {
        log.debug("分页查询考核表列表: tenantId={}, page={}, pageSize={}, searchText={}, name={}, region={}, assessmentType={}, status={}, period={}, partition={}", 
                tenantId, page, pageSize, searchText, name, region, assessmentType, status, period, partition);
        
        // 构建分页参数
        Page<AssessmentTableEntity> pageParam = new Page<>(page, pageSize);
        
        // 查询分页数据
        IPage<AssessmentTableEntity> pageResult = assessmentTableMapper.selectAssessmentTables(
                pageParam,
                tenantId != null ? tenantId.getId().toString() : null,
                searchText,
                name,
                region,
                assessmentType,
                status,
                period,
                partition
        );
        
        // 转换为DTO
        List<AssessmentTableDto> dtos = new ArrayList<>();
        if (pageResult.getRecords() != null && !pageResult.getRecords().isEmpty()) {
            dtos = pageResult.getRecords().stream()
                    .map(AssessmentTableEntity::toData)
                    .collect(Collectors.toList());
        }
        
        // 返回分页结果
        return new PageData<>(pageResult.getTotal(), dtos);
    }

    @Override
    public void deleteAssessmentTable(TenantId tenantId, UUID id) {
        log.debug("删除考核表: tenantId={}, id={}", tenantId, id);
        
        // 先删除明细项
        assessmentTableItemMapper.deleteByAssessmentTableId(id.toString());
        
        // 再删除考核表
        assessmentTableMapper.deleteById(id.toString());
    }

    @Override
    public void batchDeleteAssessmentTables(TenantId tenantId, List<UUID> ids) {
        log.debug("批量删除考核表: tenantId={}, ids={}", tenantId, ids);
        
        if (ids == null || ids.isEmpty()) {
            return;
        }
        
        // 将UUID列表转换为String列表
        List<String> stringIds = ids.stream()
                .map(UUID::toString)
                .collect(Collectors.toList());
        
        // 先删除明细项
        assessmentTableItemMapper.batchDeleteByAssessmentTableIds(stringIds);
        
        // 再删除考核表
        assessmentTableMapper.batchDeleteByIds(stringIds);
    }

    @Override
    public List<AssessmentTableItemDto> getAssessmentTableItems(TenantId tenantId, UUID assessmentTableId) {
        log.debug("查询考核表明细项列表: tenantId={}, assessmentTableId={}", tenantId, assessmentTableId);
        
        // 查询明细项
        List<AssessmentTableItemEntity> entities = assessmentTableItemMapper.selectByAssessmentTableId(assessmentTableId.toString());
        
        // 转换为DTO
        if (entities != null && !entities.isEmpty()) {
            return entities.stream()
                    .map(AssessmentTableItemEntity::toData)
                    .collect(Collectors.toList());
        }
        
        return new ArrayList<>();
    }
}