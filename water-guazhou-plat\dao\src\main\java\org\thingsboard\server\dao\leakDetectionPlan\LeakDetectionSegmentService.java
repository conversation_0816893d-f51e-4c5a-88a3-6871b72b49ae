package org.thingsboard.server.dao.leakDetectionPlan;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.thingsboard.server.dao.model.sql.leakDetectionPlan.LeakDetectionSegmentEntity;

import java.util.List;
import java.util.Map;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.thingsboard.server.dao.model.DTO.DeviceDTO;

/**
 * 探漏分段服务接口
 */
public interface LeakDetectionSegmentService {

    /**
     * 保存或更新探漏分段
     *
     * @param segment 探漏分段实体
     * @return 保存后的探漏分段
     */
    LeakDetectionSegmentEntity saveOrUpdateSegment(LeakDetectionSegmentEntity segment);

    /**
     * 根据ID查询探漏分段
     *
     * @param id 探漏分段ID
     * @return 探漏分段
     */
    LeakDetectionSegmentEntity getSegmentById(String id);

    /**
     * 根据方案ID查询探漏分段
     *
     * @param planId 方案ID
     * @return 探漏分段列表
     */
    List<LeakDetectionSegmentEntity> findByPlanId(String planId);

    /**
     * 分页查询探漏分段
     *
     * @param page        分页参数
     * @param planId      方案ID
     * @param name        分段名称
     * @param segmentCode 分段编码
     * @param status      状态
     * @param priority    优先级
     * @return 分页结果
     */
    Page<LeakDetectionSegmentEntity> querySegmentsByPage(Page<LeakDetectionSegmentEntity> page,
                                                       String planId,
                                                       String name,
                                                       String segmentCode,
                                                       String status,
                                                       Integer priority);

    /**
     * 删除探漏分段
     *
     * @param id 探漏分段ID
     * @return 是否删除成功
     */
    boolean deleteSegment(String id);

    /**
     * 批量删除探漏分段
     *
     * @param ids 探漏分段ID集合
     * @return 是否删除成功
     */
    boolean batchDeleteSegments(List<String> ids);

    /**
     * 根据方案ID删除探漏分段
     *
     * @param planId 方案ID
     * @return 是否删除成功
     */
    boolean deleteByPlanId(String planId);

    /**
     * 更新探漏分段状态
     *
     * @param id     探漏分段ID
     * @param status 状态
     * @return 是否更新成功
     */
    boolean updateSegmentStatus(String id, String status);

    /**
     * 根据方案ID统计探漏分段状态
     *
     * @param planId 方案ID
     * @return 状态统计
     */
    List<Map<String, Object>> countSegmentStatusByPlanId(String planId);
    
    /**
     * 更新探漏分段的设备绑定关系
     *
     * @param id 探漏分段ID
     * @param deviceIds 设备ID列表
     * @return 是否成功
     */
    boolean updateSegmentDevices(String id, List<String> deviceIds);
    
    /**
     * 根据条件更新探漏分段
     *
     * @param updateWrapper 更新条件包装器
     * @return 是否成功
     */
    boolean update(LambdaUpdateWrapper<LeakDetectionSegmentEntity> updateWrapper);
    
    /**
     * 获取可用设备列表（未绑定到指定分段的设备）
     *
     * @param partitionId   分区ID
     * @param boundDeviceIds 已绑定的设备ID列表
     * @param page          页码
     * @param size          每页记录数
     * @param name          设备名称
     * @param type          设备类型
     * @return 分页结果
     */
    Page<DeviceDTO> getAvailableDevices(String partitionId, List<String> boundDeviceIds, 
                                       Integer page, Integer size, String name, String type);
    
    /**
     * 根据设备ID列表获取设备详情
     *
     * @param deviceIds 设备ID列表
     * @param partitionId 分区ID
     * @return 设备详情列表
     */
    List<DeviceDTO> getDeviceDetailsByIds(List<String> deviceIds, String partitionId);
} 