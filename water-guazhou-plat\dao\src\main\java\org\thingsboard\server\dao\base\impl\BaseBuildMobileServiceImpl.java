package org.thingsboard.server.dao.base.impl;

import java.util.List;
import java.util.UUID;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.thingsboard.server.dao.base.IBaseBuildMobileService;
import org.thingsboard.server.dao.model.sql.base.BaseBuildMobile;
import org.thingsboard.server.dao.sql.base.BaseBuildMobileMapper;
import org.thingsboard.server.dao.util.imodel.query.base.BaseBuildMobilePageRequest;

/**
 * 平台管理-Mobile搭建Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Service
public class BaseBuildMobileServiceImpl implements IBaseBuildMobileService {

    @Autowired
    private BaseBuildMobileMapper baseBuildMobileMapper;

    /**
     * 查询平台管理-Mobile搭建
     *
     * @param id 平台管理-Mobile搭建主键
     * @return 平台管理-Mobile搭建
     */
    @Override
    public BaseBuildMobile selectBaseBuildMobileById(String id) {
        return baseBuildMobileMapper.selectBaseBuildMobileById(id);
    }

    /**
     * 查询平台管理-Mobile搭建列表
     *
     * @param baseBuildMobile 平台管理-Mobile搭建
     * @return 平台管理-Mobile搭建
     */
    @Override
    public IPage<BaseBuildMobile> selectBaseBuildMobileList(BaseBuildMobilePageRequest baseBuildMobile) {
        return baseBuildMobileMapper.selectBaseBuildMobileList(baseBuildMobile);
    }

    /**
     * 新增平台管理-Mobile搭建
     *
     * @param baseBuildMobile 平台管理-Mobile搭建
     * @return 结果
     */
    @Override
    public int insertBaseBuildMobile(BaseBuildMobile baseBuildMobile) {
        baseBuildMobile.setId(UUID.randomUUID().toString().replace("-", ""));
        return baseBuildMobileMapper.insertBaseBuildMobile(baseBuildMobile);
    }

    /**
     * 修改平台管理-Mobile搭建
     *
     * @param baseBuildMobile 平台管理-Mobile搭建
     * @return 结果
     */
    @Override
    public int updateBaseBuildMobile(BaseBuildMobile baseBuildMobile) {
        return baseBuildMobileMapper.updateBaseBuildMobile(baseBuildMobile);
    }

    /**
     * 批量删除平台管理-Mobile搭建
     *
     * @param ids 需要删除的平台管理-Mobile搭建主键
     * @return 结果
     */
    @Override
    public int deleteBaseBuildMobileByIds(List<String> ids) {
        return baseBuildMobileMapper.deleteBaseBuildMobileByIds(ids);
    }

    /**
     * 删除平台管理-Mobile搭建信息
     *
     * @param id 平台管理-Mobile搭建主键
     * @return 结果
     */
    @Override
    public int deleteBaseBuildMobileById(String id) {
        return baseBuildMobileMapper.deleteBaseBuildMobileById(id);
    }
}
