package org.thingsboard.server.dao.orderWork;

import org.thingsboard.server.common.data.id.TenantId;
import org.thingsboard.server.dao.model.DTO.TreeNodeDTO;
import org.thingsboard.server.dao.model.sql.workOrder.WorkOrderType;

import java.util.List;

public interface WorkOrderTypeService {
    List<TreeNodeDTO> findList(String status, TenantId tenantId);

    void save(WorkOrderType entity);

    void changeStatus(String status, String id);

    List<TreeNodeDTO> findByResourceId(String resourceId);

    List<String> findTypeIdByResourceId(String resourceId);
}
