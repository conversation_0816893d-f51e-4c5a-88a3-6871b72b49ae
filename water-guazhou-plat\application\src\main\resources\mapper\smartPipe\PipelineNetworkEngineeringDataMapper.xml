<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.thingsboard.server.dao.sql.smartPipe.PipelineNetworkEngineeringDataMapper">
    
    <resultMap type="org.thingsboard.server.dao.model.sql.smartPipe.PipelineNetworkEngineeringData" id="PipelineNetworkEngineeringDataResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="code"    column="code"    />
        <result property="nameAbb"    column="name_abb"    />
        <result property="properties"    column="properties"    />
        <result property="area"    column="area"    />
        <result property="measurementUnit"    column="measurement_unit"    />
        <result property="assignEmployees"    column="assign_employees"    />
        <result property="worksScope"    column="works_scope"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="tenantId"    column="tenant_id"    />
        <result property="isCommit"    column="is_commit"    />
        <result property="isAudit"    column="is_audit"    />
        <result property="isStorage"    column="is_storage"    />
    </resultMap>

    <sql id="selectPipelineNetworkEngineeringDataVo">
        select id, name, code, name_abb, properties, area, measurement_unit, assign_employees, works_scope, start_time, end_time, create_time, update_time, tenant_id, is_commit, is_audit, is_storage from pipeline_network_engineering_data
    </sql>

    <select id="selectPipelineNetworkEngineeringDataList" parameterType="org.thingsboard.server.dao.model.sql.smartPipe.PipelineNetworkEngineeringData" resultMap="PipelineNetworkEngineeringDataResult">
        <include refid="selectPipelineNetworkEngineeringDataVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="code != null  and code != ''"> and code = #{code}</if>
            <if test="nameAbb != null  and nameAbb != ''"> and name_abb = #{nameAbb}</if>
            <if test="properties != null  and properties != ''"> and properties = #{properties}</if>
            <if test="area != null  and area != ''"> and area = #{area}</if>
            <if test="measurementUnit != null  and measurementUnit != ''"> and measurement_unit like concat('%', #{measurementUnit}, '%')</if>
            <if test="assignEmployees != null  and assignEmployees != ''"> and assign_employees = #{assignEmployees}</if>
            <if test="worksScope != null  and worksScope != ''"> and works_scope = #{worksScope}</if>
            <if test="startTime != null "> and start_time = #{startTime}</if>
            <if test="endTime != null "> and end_time = #{endTime}</if>
            <if test="tenantId != null  and tenantId != ''"> and tenant_id = #{tenantId}</if>
            <if test="isCommit != null  and isCommit != ''"> and is_commit = #{isCommit}</if>
            <if test="isAudit != null  and isAudit != ''"> and is_audit = #{isAudit}</if>
            <if test="isStorage != null  and isStorage != ''"> and is_storage = #{isStorage}</if>
        </where>
    </select>
    
    <select id="selectPipelineNetworkEngineeringDataById" parameterType="String" resultMap="PipelineNetworkEngineeringDataResult">
        <include refid="selectPipelineNetworkEngineeringDataVo"/>
        where id = #{id}
    </select>

    <insert id="insertPipelineNetworkEngineeringData" parameterType="org.thingsboard.server.dao.model.sql.smartPipe.PipelineNetworkEngineeringData">
        insert into pipeline_network_engineering_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="name != null">name,</if>
            <if test="code != null">code,</if>
            <if test="nameAbb != null">name_abb,</if>
            <if test="properties != null">properties,</if>
            <if test="area != null">area,</if>
            <if test="measurementUnit != null">measurement_unit,</if>
            <if test="assignEmployees != null">assign_employees,</if>
            <if test="worksScope != null">works_scope,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            create_time,
            update_time,
            <if test="tenantId != null">tenant_id,</if>
            <if test="isCommit != null">is_commit,</if>
            <if test="isAudit != null">is_audit,</if>
            <if test="isStorage != null">is_storage,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="name != null">#{name},</if>
            <if test="code != null">#{code},</if>
            <if test="nameAbb != null">#{nameAbb},</if>
            <if test="properties != null">#{properties},</if>
            <if test="area != null">#{area},</if>
            <if test="measurementUnit != null">#{measurementUnit},</if>
            <if test="assignEmployees != null">#{assignEmployees},</if>
            <if test="worksScope != null">#{worksScope},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            NOW(),
            NOW(),
            <if test="tenantId != null">#{tenantId},</if>
            <if test="isCommit != null">#{isCommit},</if>
            <if test="isAudit != null">#{isAudit},</if>
            <if test="isStorage != null">#{isStorage},</if>
         </trim>
    </insert>

    <update id="updatePipelineNetworkEngineeringData" parameterType="org.thingsboard.server.dao.model.sql.smartPipe.PipelineNetworkEngineeringData">
        update pipeline_network_engineering_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="code != null">code = #{code},</if>
            <if test="nameAbb != null">name_abb = #{nameAbb},</if>
            <if test="properties != null">properties = #{properties},</if>
            <if test="area != null">area = #{area},</if>
            <if test="measurementUnit != null">measurement_unit = #{measurementUnit},</if>
            <if test="assignEmployees != null">assign_employees = #{assignEmployees},</if>
            <if test="worksScope != null">works_scope = #{worksScope},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            update_time = NOW(),
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
            <if test="isCommit != null">is_commit = #{isCommit},</if>
            <if test="isAudit != null">is_audit = #{isAudit},</if>
            <if test="isStorage != null">is_storage = #{isStorage},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePipelineNetworkEngineeringDataById" parameterType="String">
        delete from pipeline_network_engineering_data where id = #{id}
    </delete>

    <delete id="deletePipelineNetworkEngineeringDataByIds" parameterType="String">
        delete from pipeline_network_engineering_data where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>