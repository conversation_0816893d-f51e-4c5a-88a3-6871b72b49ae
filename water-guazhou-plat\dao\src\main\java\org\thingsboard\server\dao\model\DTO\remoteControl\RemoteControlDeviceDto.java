package org.thingsboard.server.dao.model.DTO.remoteControl;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 远程控制设备DTO
 *
 * <AUTHOR>
 * @date 2025-01-28
 */
@Data
@ApiModel(description = "远程控制设备")
public class RemoteControlDeviceDto {

    @ApiModelProperty(value = "设备ID")
    private String id;

    @ApiModelProperty(value = "设备ID", required = true)
    private String deviceId;

    @ApiModelProperty(value = "设备名称", required = true)
    private String deviceName;

    @ApiModelProperty(value = "设备编码", required = true)
    private String deviceCode;

    @ApiModelProperty(value = "水厂ID", required = true)
    private String waterPlantId;

    @ApiModelProperty(value = "水厂名称", required = true)
    private String waterPlantName;

    @ApiModelProperty(value = "设备类型", required = true, notes = "pump/valve/monitor/controller")
    private String deviceType;

    @ApiModelProperty(value = "设备位置")
    private String location;

    @ApiModelProperty(value = "设备状态", notes = "1:在线, 0:离线, 2:故障, 3:维护中")
    private Integer status;

    @ApiModelProperty(value = "安装日期")
    private Date installDate;

    @ApiModelProperty(value = "制造商")
    private String manufacturer;

    @ApiModelProperty(value = "型号")
    private String model;

    @ApiModelProperty(value = "设备描述")
    private String description;

    @ApiModelProperty(value = "控制记录列表")
    private List<DeviceControlRecordDto> controlRecords;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}
