package org.thingsboard.server.dao.leakDetectionPlan;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.thingsboard.server.dao.model.DTO.LeakDetectionPlanDTO;
import org.thingsboard.server.dao.model.sql.leakDetectionPlan.LeakDetectionPlanEntity;
import org.thingsboard.server.dao.model.sql.leakDetectionPlan.LeakDetectionSegmentEntity;
import org.thingsboard.server.dao.sql.leakDetectionPlan.LeakDetectionPlanDao;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 探漏方案服务实现类
 */
@Service
@Slf4j
public class LeakDetectionPlanServiceImpl implements LeakDetectionPlanService {

    @Autowired
    private LeakDetectionPlanDao leakDetectionPlanDao;
    
    @Autowired
    private LeakDetectionSegmentService leakDetectionSegmentService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public LeakDetectionPlanEntity saveOrUpdatePlan(LeakDetectionPlanEntity plan) {
        log.info("Save or update leak detection plan: {}", plan);
        
        // 校验开始时间和结束时间
        if (plan.getStartTime() != null && plan.getEndTime() != null && 
            plan.getStartTime().after(plan.getEndTime())) {
            throw new IllegalArgumentException("开始时间不能大于结束时间");
        }
        
        if (StringUtils.isEmpty(plan.getId())) {
            // 新增
            plan.setId(UUID.randomUUID().toString());
            plan.setCreatedTime(new Date());
            plan.setDeleted(0);
            
            leakDetectionPlanDao.insert(plan);
        } else {
            // 更新
            LeakDetectionPlanEntity oldPlan = leakDetectionPlanDao.selectById(plan.getId());
            if (oldPlan == null) {
                throw new IllegalArgumentException("探漏方案不存在");
            }
            
            plan.setUpdatedTime(new Date());
            plan.setDeleted(oldPlan.getDeleted());
            plan.setCreatedTime(oldPlan.getCreatedTime());
            plan.setCreatedBy(oldPlan.getCreatedBy());
            
            leakDetectionPlanDao.updateById(plan);
        }
        
        return plan;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public LeakDetectionPlanDTO saveOrUpdatePlanWithSegments(LeakDetectionPlanDTO planDTO) {
        log.info("Save or update leak detection plan with segments: {}", planDTO);
        
        if (planDTO == null || planDTO.getPlan() == null) {
            throw new IllegalArgumentException("探漏方案不能为空");
        }
        
        // 保存探漏方案
        LeakDetectionPlanEntity savedPlan = saveOrUpdatePlan(planDTO.getPlan());
        
        // 处理分段数据
        List<LeakDetectionSegmentEntity> savedSegments = new ArrayList<>();
        if (planDTO.getSegments() != null && !planDTO.getSegments().isEmpty()) {
            for (LeakDetectionSegmentEntity segment : planDTO.getSegments()) {
                // 设置分段关联的方案ID
                segment.setPlanId(savedPlan.getId());
                // 保存分段数据
                LeakDetectionSegmentEntity savedSegment = leakDetectionSegmentService.saveOrUpdateSegment(segment);
                savedSegments.add(savedSegment);
            }
        }
        
        // 构建返回结果
        LeakDetectionPlanDTO result = new LeakDetectionPlanDTO();
        result.setPlan(savedPlan);
        result.setSegments(savedSegments);
        
        return result;
    }

    @Override
    public LeakDetectionPlanEntity getPlanById(String id) {
        log.info("Get leak detection plan by id: {}", id);
        return leakDetectionPlanDao.selectById(id);
    }

    @Override
    public LeakDetectionPlanDTO getPlanWithSegmentsById(String id) {
        log.info("Get leak detection plan with segments by id: {}", id);
        
        // 获取探漏方案
        LeakDetectionPlanEntity plan = getPlanById(id);
        if (plan == null) {
            return null;
        }
        
        // 获取关联的分段数据
        List<LeakDetectionSegmentEntity> segments = leakDetectionSegmentService.findByPlanId(id);
        
        // 构建返回结果
        LeakDetectionPlanDTO result = new LeakDetectionPlanDTO();
        result.setPlan(plan);
        result.setSegments(segments);
        
        return result;
    }

    @Override
    public Page<LeakDetectionPlanEntity> queryPlansByPage(Page<LeakDetectionPlanEntity> page, 
                                                        String name, 
                                                        String areaCode, 
                                                        String partitionId,
                                                        String status, 
                                                        String startTime, 
                                                        String endTime) {
        log.info("Query leak detection plans by page, name: {}, areaCode: {}, partitionId: {}, status: {}, startTime: {}, endTime: {}", 
                name, areaCode, partitionId, status, startTime, endTime);
        
        // 构建查询条件
        LambdaQueryWrapper<LeakDetectionPlanEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LeakDetectionPlanEntity::getDeleted, 0);
        
        // 添加查询条件
        if (StringUtils.isNotEmpty(name)) {
            queryWrapper.like(LeakDetectionPlanEntity::getName, name);
        }
        if (StringUtils.isNotEmpty(areaCode)) {
            queryWrapper.eq(LeakDetectionPlanEntity::getAreaCode, areaCode);
        }
        if (StringUtils.isNotEmpty(partitionId)) {
            queryWrapper.eq(LeakDetectionPlanEntity::getPartitionId, partitionId);
        }
        if (StringUtils.isNotEmpty(status)) {
            queryWrapper.eq(LeakDetectionPlanEntity::getStatus, status);
        }
        
        // 处理时间范围
        if (StringUtils.isNotEmpty(startTime)) {
            queryWrapper.ge(LeakDetectionPlanEntity::getStartTime, startTime);
        }
        if (StringUtils.isNotEmpty(endTime)) {
            queryWrapper.le(LeakDetectionPlanEntity::getEndTime, endTime);
        }
        
        // 按创建时间倒序排序
        queryWrapper.orderByDesc(LeakDetectionPlanEntity::getCreatedTime);
        
        // 执行分页查询
        return leakDetectionPlanDao.selectPage(page, queryWrapper);
    }

    @Override
    public List<LeakDetectionPlanEntity> findByAreaCode(String areaCode) {
        log.info("Find leak detection plans by area code: {}", areaCode);
        
        LambdaQueryWrapper<LeakDetectionPlanEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LeakDetectionPlanEntity::getAreaCode, areaCode)
                   .eq(LeakDetectionPlanEntity::getDeleted, 0)
                   .orderByDesc(LeakDetectionPlanEntity::getCreatedTime);
        
        return leakDetectionPlanDao.selectList(queryWrapper);
    }
    
    @Override
    public List<LeakDetectionPlanEntity> findByPartitionId(String partitionId) {
        log.info("Find leak detection plans by partition id: {}", partitionId);
        
        LambdaQueryWrapper<LeakDetectionPlanEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LeakDetectionPlanEntity::getPartitionId, partitionId)
                   .eq(LeakDetectionPlanEntity::getDeleted, 0)
                   .orderByDesc(LeakDetectionPlanEntity::getCreatedTime);
        
        return leakDetectionPlanDao.selectList(queryWrapper);
    }

    @Override
    public List<LeakDetectionPlanEntity> findByStatus(String status) {
        log.info("Find leak detection plans by status: {}", status);
        
        LambdaQueryWrapper<LeakDetectionPlanEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LeakDetectionPlanEntity::getStatus, status)
                   .eq(LeakDetectionPlanEntity::getDeleted, 0)
                   .orderByDesc(LeakDetectionPlanEntity::getCreatedTime);
        
        return leakDetectionPlanDao.selectList(queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deletePlan(String id) {
        log.info("Delete leak detection plan by id: {}", id);
        
        // 先删除关联的分段
        leakDetectionSegmentService.deleteByPlanId(id);
        
        // 逻辑删除方案
        LeakDetectionPlanEntity plan = leakDetectionPlanDao.selectById(id);
        if (plan == null) {
            return false;
        }
        
        plan.setDeleted(1);
        plan.setUpdatedTime(new Date());
        return leakDetectionPlanDao.updateById(plan) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deletePlanWithSegments(String id) {
        log.info("Delete leak detection plan with segments by id: {}", id);
        
        // 先删除关联的分段数据
        List<LeakDetectionSegmentEntity> segments = leakDetectionSegmentService.findByPlanId(id);
        if (segments != null && !segments.isEmpty()) {
            for (LeakDetectionSegmentEntity segment : segments) {
                leakDetectionSegmentService.deleteSegment(segment.getId());
            }
        }
        
        // 再删除探漏方案
        return deletePlan(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeletePlans(List<String> ids) {
        log.info("Batch delete leak detection plans: {}", ids);
        
        if (ids == null || ids.isEmpty()) {
            return false;
        }
        
        for (String id : ids) {
            // 调用deletePlan方法删除每个方案及其关联的分段
            deletePlan(id);
        }
        
        return true;
    }

    @Override
    public boolean updatePlanStatus(String id, String status) {
        log.info("Update leak detection plan status, id: {}, status: {}", id, status);
        
        if (StringUtils.isEmpty(id) || StringUtils.isEmpty(status)) {
            return false;
        }
        
        LambdaUpdateWrapper<LeakDetectionPlanEntity> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(LeakDetectionPlanEntity::getId, id)
                    .set(LeakDetectionPlanEntity::getStatus, status)
                    .set(LeakDetectionPlanEntity::getUpdatedTime, new Date());
        
        return leakDetectionPlanDao.update(null, updateWrapper) > 0;
    }
}